import React from 'react';
import OtpChannelVerificationScreen from '@/app/login/components/OtpChannelVerificationScreen';
// import { useTheme } from '@/contexts/ThemeContext'; // Removed
import { appStyleStore } from 'stores/app_style_store'; // Added aliased import

export default function PhoneVerificationScreen() {
  // const { theme } = useTheme(); // Removed
  const theme = appStyleStore(state => state.theme); // Added and typed

  // Define the theme properties for SMS, derived from the global theme
  // This ensures that if customScreenTheme is not explicitly WhatsApp's, it uses global defaults
  const smsScreenTheme = {
    primary: theme.colors.primary,
    primaryDark: theme.colors.primary, // Assuming primaryDark can be same as primary for default
    primaryDisabled: theme.colors.primaryDisabled,
    background: theme.system.background,
    text: theme.system.text,
    secondaryText: theme.system.secondaryText,
    error: theme.colors.error,
  };

  return (
    <OtpChannelVerificationScreen
      channel="sms"
      customScreenTheme={smsScreenTheme} // Pass the derived theme
      headerIconName="cellphone-message"
      screenTitleKey="auth.smsVerification"      // Used for Stack.Screen headerTitle
      headerTitleKey="auth.smsVerification"      // Used for the title text in component body
      headerSubtitleKey="auth.smsVerificationDesc"
      infoTextKey="auth.registrationInfo"
      buttonTextKey="common.continue"
    />
  );
}

// Original StyleSheet can be removed if all styles are handled by the generic component
// or if specific overrides were minor and are now handled by theme props.