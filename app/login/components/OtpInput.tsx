import React, { useRef, useEffect } from 'react';
import {
  View,
  Text,
  TextInput,
  StyleSheet,
  Pressable,
  Keyboard,
  Platform,
} from 'react-native';
// import { useTheme } from '@/contexts/ThemeContext'; // Removed
import { appStyleStore } from 'stores/app_style_store'; // Added aliased import
import { whatsAppTheme } from 'theme/index';

interface OtpInputProps {
  codeLength: number;
  verificationCode: string;
  onVerificationCodeChange: (code: string) => void;
  onSubmit?: () => void; // Optional: if auto-submit on full code is desired
  inputRef?: React.RefObject<TextInput | null>; // Allow null for the ref object value
  verificationMethod?: 'sms' | 'whatsapp';
}

export default function OtpInput({
  codeLength,
  verificationCode,
  onVerificationCodeChange,
  onSubmit,
  inputRef, // Use the passed ref or create a local one
  verificationMethod = 'sms',
}: OtpInputProps) {
  // const { theme: globalTheme } = useTheme(); // Removed
  const globalTheme = appStyleStore(state => state.theme); // Added and typed
  // Use the WhatsApp theme if the verification method is WhatsApp
  const theme = verificationMethod === 'whatsapp' ? whatsAppTheme : globalTheme;
  
  const localInputRef = useRef<TextInput>(null);
  const currentInputRef = inputRef || localInputRef;

  useEffect(() => {
    // Auto focus the hidden input on mount if it's the primary ref
    // Add a slight delay to ensure the component is fully rendered
    const focusTimeout = setTimeout(() => {
      currentInputRef.current?.focus();
    }, Platform.OS === 'ios' ? 100 : 300); // Shorter delay for iOS, slightly longer for Android potentially

    return () => clearTimeout(focusTimeout); // Clear timeout on unmount
  }, [currentInputRef]);

  const handlePressableFocus = () => {
    currentInputRef.current?.focus();
  };

  const handleCodeChange = (text: string) => {
    const newCode = text.replace(/[^0-9]/g, '');
    const finalCode = newCode.slice(0, codeLength);
    onVerificationCodeChange(finalCode);

    if (finalCode.length === codeLength) {
      Keyboard.dismiss();
      if (onSubmit) {
        onSubmit(); // Call onSubmit if provided and code is complete
      }
    }
  };

  const renderCodeBoxes = () => {
    const boxes = [];
    for (let i = 0; i < codeLength; i++) {
      const digit = verificationCode[i] || '';
      // isFocused should highlight the current input position or the first empty box
      const isFocused = i === verificationCode.length && verificationCode.length < codeLength;
      const hasDigit = !!digit;

      boxes.push(
        <View
          key={i}
          style={[
            styles.codeInput,
            { 
              borderColor: theme.system.border,
              backgroundColor: theme.system.background, // Default background
              borderStyle: hasDigit ? 'solid' : 'dashed',
            },
            (hasDigit || isFocused) && {
              borderColor: theme.colors.primary,
            },
            hasDigit && { // Different style for filled box if desired
                backgroundColor: theme.colors.primaryContainer, 
            },
            isFocused && !hasDigit && { // Style for the current empty box to type into
                borderColor: theme.colors.primary, // Highlight border
                // Optionally add a different background for the focused empty box
            }
          ]}
        >
          <Text style={[styles.codeInputText, { color: theme.system.text }]}>
            {digit}
          </Text>
        </View>
      );
    }
    return boxes;
  };

  return (
    <View style={styles.container}>
      <Pressable style={styles.codeContainer} onPress={handlePressableFocus}>
        {renderCodeBoxes()}
      </Pressable>
      <TextInput
        ref={currentInputRef}
        style={styles.hiddenInput}
        value={verificationCode}
        onChangeText={handleCodeChange}
        keyboardType="number-pad"
        maxLength={codeLength}
        textContentType="oneTimeCode" // For iOS autofill
        autoComplete={Platform.OS === 'android' ? 'sms-otp' : 'one-time-code'} // Android vs iOS
        caretHidden // Hide the caret visually
        selectionColor={'transparent'} // Hide selection highlight
        autoFocus={false} // Controlled by useEffect
      />
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    alignItems: 'center',
    width: '100%',
  },
  codeContainer: {
    flexDirection: 'row',
    justifyContent: 'center',
    gap: 10, // Spacing between boxes
    marginBottom: 24, // Match original spacing
    width: '100%', // Ensure it takes full width for centering
  },
  codeInput: {
    width: 45, // Standard box size
    height: 45,
    borderWidth: 2,
    borderRadius: 8,
    alignItems: 'center',
    justifyContent: 'center',
  },
  codeInputText: {
    fontSize: 20,
    fontWeight: '600',
  },
  hiddenInput: {
    position: 'absolute',
    top: 0,
    left: 0,
    width: 1, // Minimal size to be non-intrusive
    height: 1,
    opacity: 0, // Completely invisible
    color: 'transparent',
    backgroundColor: 'transparent',
    borderWidth: 0,
    padding: 0,
    margin: 0,
  },
}); 