import React, { useMemo, useCallback } from 'react';
import { Tabs } from 'expo-router';
import { MaterialCommunityIcons } from '@expo/vector-icons';
import { useTranslation } from 'react-i18next';
import { appStyleStore } from 'stores/app_style_store';
import { authenticationStore } from 'stores/authentication_store';
import { View, Platform } from 'react-native';

export default function TabLayout() {
  const { t } = useTranslation();
  const activeTheme = appStyleStore(state => state.theme);
  const isAuthenticated = authenticationStore(state => state.isAuthenticated);

  return (
    <View style={{ flex: 1, backgroundColor: activeTheme.system.background }}>
      <Tabs
        screenOptions={{
          tabBarActiveTintColor: activeTheme.colors.primary,
          tabBarInactiveTintColor: '#666',
          tabBarStyle: {
            height: Platform.OS === 'ios' ? 83 : 60,
            paddingBottom: Platform.OS === 'ios' ? 34 : 0,
            paddingTop: 0,
            backgroundColor: activeTheme.colors.background,
            borderTopWidth: 1,
            borderTopColor: activeTheme.system.border,
          },
        }}
      >
        {/* Dashboard */}
        <Tabs.Screen
          name="dashboard"
          options={{
            title: t('navigation.dashboard'),
            headerShown: false,
            tabBarIcon: ({ color, size }: { color: string; size: number }) => (
              <MaterialCommunityIcons name="home" size={size} color={color} />
            ),
          }}
        />
        
        {/* Explore */}
        <Tabs.Screen
          name="explore"
          options={{
            title: t('navigation.explore'),
            headerShown: false,
            tabBarIcon: ({ color, size }: { color: string; size: number }) => (
              <MaterialCommunityIcons name="compass-outline" size={size} color={color} />
            ),
          }}
        />

        {/* Login tab - Hidden if authenticated by making href null */}
        <Tabs.Screen
          name="login"
          options={
            isAuthenticated
              ? { href: null }
              : {
                  title: t('navigation.login'),
                  headerShown: false,
                  tabBarIcon: ({ color, size }: { color: string; size: number }) => (
                    <MaterialCommunityIcons name="login" size={size} color={color} />
                  ),
                }
          }
          listeners={
            isAuthenticated
              ? {
                  tabPress: (e: any) => {
                    e.preventDefault();
                  },
                }
              : undefined
          }
        />

        {/* QR Code - Hidden if not authenticated by making href null */}
        <Tabs.Screen
          name="qrcode"
          options={
            !isAuthenticated
              ? { href: null }
              : {
                  title: '',
                  headerShown: false,
                  tabBarIcon: ({ focused }: { focused: boolean }) => (
                    <View
                      style={{
                        width: 60,
                        height: 60,
                        backgroundColor: focused ? activeTheme.colors.secondary : activeTheme.colors.primary,
                        borderRadius: 30,
                        justifyContent: 'center',
                        alignItems: 'center',
                        marginBottom: 25,
                        borderWidth: 4,
                        borderColor: activeTheme.colors.background,
                        shadowColor: '#000',
                        shadowOffset: { width: 0, height: 2 },
                        shadowOpacity: 0.25,
                        shadowRadius: 3.84,
                        elevation: 5,
                      }}
                    >
                      <MaterialCommunityIcons
                        name="qrcode"
                        size={32}
                        color="#FFFFFF"
                        style={{ opacity: focused ? 0.9 : 1 }}
                      />
                    </View>
                  ),
                }
          }
          listeners={
            !isAuthenticated
              ? {
                  tabPress: (e: any) => {
                    e.preventDefault();
                  },
                }
              : undefined
          }
        />

        {/* My Events - Hidden if not authenticated by making href null */}
        <Tabs.Screen
          name="myEvents"
          options={
            !isAuthenticated
              ? { href: null }
              : {
                  title: t('navigation.myEvents'),
                  headerShown: false,
                  tabBarIcon: ({ color, size }: { color: string; size: number }) => (
                    <MaterialCommunityIcons name="calendar" size={size} color={color} />
                  ),
                }
          }
          listeners={
            !isAuthenticated
              ? {
                  tabPress: (e: any) => {
                    e.preventDefault();
                  },
                }
              : undefined
          }
        />

        {/* Profile - Hidden if not authenticated by making href null */}
        <Tabs.Screen
          name="profile"
          options={
            !isAuthenticated
              ? { href: null }
              : {
                  title: t('navigation.profile'),
                  headerShown: false,
                  tabBarIcon: ({ color, size }: { color: string; size: number }) => (
                    <MaterialCommunityIcons name="account" size={size} color={color} />
                  ),
                }
          }
          listeners={
            !isAuthenticated
              ? {
                  tabPress: (e: any) => {
                    e.preventDefault();
                  },
                }
              : undefined
          }
        />
      </Tabs>
    </View>
  );
}
