import React, { useState } from 'react';
import { View, Text, StyleSheet, TouchableOpacity, ScrollView, Alert, Platform, SafeAreaView } from 'react-native';
import { Image } from 'expo-image';
import { useTranslation } from 'react-i18next';
import MaterialCommunityIcons from '@expo/vector-icons/MaterialCommunityIcons';
import { useRouter, useLocalSearchParams, Stack } from 'expo-router';
import * as ImagePicker from 'expo-image-picker';
import { StatusBar } from 'expo-status-bar';
import { appStyleStore } from 'stores/app_style_store';
import { CustomDialog } from '@/common_modules/CustomDialog';
import { VerificationTypeEnum } from 'types/enums';
import { useSubmitVerification, useFetchUserVerifications, useSubmitVerificationForUser } from '@/api/user_services';
import * as FileSystem from 'expo-file-system';
import { createTheme } from 'theme/index';
import { 
  appendMappedParamsToFormData, 
  getFileParameterNames 
} from './verificationParameterMapper';

type RouteParams = {
  formData: string; // Expect stringified JSON
  frontImageUri: string; // Changed from frontImage (base64) to frontImageUri
  documentType: VerificationTypeEnum; // Use enum
  assistingMemberId?: string;
  isVolunteerAssisting?: string;
};

const MAX_FILE_SIZE = 10 * 1024 * 1024; // 10MB in bytes

export default function IdentityBackScreen() {
  const { t } = useTranslation();
  const theme = appStyleStore(state => state.theme || createTheme('red'));
  const router = useRouter();
  const params = useLocalSearchParams<RouteParams>();
  const { frontImageUri, documentType = VerificationTypeEnum.HkIDCard } = params;
  const parsedFormData = params.formData ? JSON.parse(params.formData) : {};
  
  // Check if we're in volunteer assistance mode
  const assistingMemberId = params.assistingMemberId as string;
  const isVolunteerAssisting = params.isVolunteerAssisting === 'true';
  
  const { refetch: refetchVerifications } = useFetchUserVerifications();
  const submitVerificationMutation = useSubmitVerification();
  const submitVerificationForUserMutation = useSubmitVerificationForUser();

  const [backImageUri, setBackImageUri] = useState<string | null>(null);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [successDialogVisible, setSuccessDialogVisible] = useState(false);
  const [errorDialogVisible, setErrorDialogVisible] = useState(false);
  const [errorMessage, setErrorMessage] = useState('');

  const getSampleImage = () => {
    switch (documentType) {
      case VerificationTypeEnum.MainlandChinaIDCard:
        return require('@/assets/verification-samples/mainland-id-back-sample.png');
      case VerificationTypeEnum.MainlandTravelPermit:
        return require('@/assets/verification-samples/mainland-travel-permit-back-sample.jpg');
      case VerificationTypeEnum.HkIDCard:
      default:
        return require('@/assets/verification-samples/id-card-back-sample.png');
    }
  };

  const getTitle = () => {
    return t(`identity.steps.back.${documentType}.title`, t('identity.steps.back.default.title'));
  };
  const getSampleCaption = () => {
    return t(`identity.steps.back.${documentType}.sample`, t('identity.steps.back.default.sample'));
  };
  const styles = StyleSheet.create({
    container: {
      flex: 1,
      backgroundColor: theme.system.background,
    },
    scrollView: {
      flex: 1,
    },
    scrollContent: {
      flexGrow: 1,
    },
    content: {
      padding: 24,
      flex: 1, 
      justifyContent: 'space-between',
    },
    section: {
      flex: 1,
    },
    sectionTitle: {
      fontSize: 20,
      fontWeight: '600',
      color: theme.system.text,
      marginBottom: 8,
      textAlign: 'center',
    },
    sectionDescription: {
      fontSize: 15,
      color: theme.system.secondaryText,
      marginBottom: 16,
      textAlign: 'center',
    },
    sampleImageContainer: {
      alignItems: 'center',
      marginBottom: 16,
    },
    sampleImage: {
      width: '80%',
      height: undefined,
      aspectRatio: 3/2,
      borderRadius: 8,
    },
    sampleCaption: {
      fontSize: 14,
      color: theme.system.secondaryText,
      textAlign: 'center',
      marginTop: -16,
      marginBottom: 24,
    },
    uploadButton: {
      height: 200,
      borderWidth: 1,
      borderColor: 'gray',
      borderRadius: 12,
      borderStyle: 'dashed',
      alignItems: 'center',
      justifyContent: 'center',
      backgroundColor: '#F5F5F5',
      overflow: 'hidden',
    },
    uploadButtonWithImage: {
      borderStyle: 'solid',
      backgroundColor: 'transparent',
    },
    uploadButtonText: {
      marginTop: 8,
      fontSize: 15,
      color: theme.colors.primary,
    },
    previewImage: {
      width: '100%',
      height: '100%',
      borderRadius: 11,
    },
    footer: {
      paddingHorizontal: 24,
      paddingVertical: 16,
      borderTopWidth: 1,
      borderTopColor: theme.system.border,
      backgroundColor: theme.system.background,
    },
    submitButton: {
      backgroundColor: theme.colors.primary,
      paddingVertical: 16,
      borderRadius: 12,
      alignItems: 'center',
      justifyContent: 'center',
      flexDirection: 'row',
    },
    submitButtonDisabled: {
      backgroundColor: theme.colors.primaryDisabled || '#CCCCCC',
    },
    submitButtonText: {
      fontSize: 17,
      fontWeight: '600',
      color: '#FFFFFF',
    },
  });

  const requestPermissions = async () => {
    if (Platform.OS !== 'web') {
      const { status: cameraStatus } = await ImagePicker.requestCameraPermissionsAsync();
      const { status: libraryStatus } = await ImagePicker.requestMediaLibraryPermissionsAsync();
      
      if (cameraStatus !== 'granted') {
        Alert.alert(
          t('common.error'),
          t('identity.permissions.camera'),
          [{ text: t('common.ok') }]
        );
        return false;
      }
      
      if (libraryStatus !== 'granted') {
        Alert.alert(
          t('common.error'),
          t('identity.permissions.gallery'),
          [{ text: t('common.ok') }]
        );
        return false;
      }
    }
    return true;
  };

  const pickImage = async (method: 'camera' | 'gallery') => {
    const hasPermissions = await requestPermissions();
    if (!hasPermissions) return;

    try {
      const options: ImagePicker.ImagePickerOptions = {
        allowsEditing: true,
        aspect: [3, 2], // Assuming back images are similar aspect to front HKID/Permit
        quality: 0.7, // Match front screen quality
        // base64: false, // Don't need base64
      };

      const result = method === 'camera'
        ? await ImagePicker.launchCameraAsync(options)
        : await ImagePicker.launchImageLibraryAsync(options);

      if (!result.canceled && result.assets && result.assets.length > 0 && result.assets[0].uri) {
        const originalUri = result.assets[0].uri;
        const fileInfo = await FileSystem.getInfoAsync(originalUri, { size: true });
        
        if (fileInfo.exists && fileInfo.size && fileInfo.size > MAX_FILE_SIZE) {
          setErrorMessage(t('identity.errors.fileTooLarge'));
          setErrorDialogVisible(true);
          return;
        }

        let stableUri = originalUri;
        // Immediately copy to a stable location on Android if it's a file URI
        if (Platform.OS === 'android' && originalUri.startsWith('file://')) {
          const fileName = originalUri.split('/').pop() || `pickedImage_${Date.now()}.jpg`;
          // Use a dedicated subdirectory in cache or document directory
          const permDir = FileSystem.cacheDirectory + 'AppPickedImages/'; 
          await FileSystem.makeDirectoryAsync(permDir, { intermediates: true });
          stableUri = permDir + fileName;
          try {
            await FileSystem.copyAsync({ from: originalUri, to: stableUri });
          } catch (copyError) {
            console.error('Error copying picked image immediately:', copyError);
            // If immediate copy fails, fall back to original URI but expect potential issues
            setErrorMessage(t('identity.errors.upload') + ' (Copy Error)');
            setErrorDialogVisible(true);
            stableUri = originalUri; // Fallback, though likely problematic
          }
        }
        setBackImageUri(stableUri); // Store URI (potentially of the copied file)
      }
    } catch (error) {
       console.error("Image picking/processing error:", error);
      setErrorMessage(method === 'camera'
        ? t('identity.errors.capture')
        : t('identity.errors.upload'));
      setErrorDialogVisible(true);
    }
  };

  const showUploadOptions = () => {
    Alert.alert(
      t('identity.upload.title'),
      '',
      [
        {
          text: t('common.cancel'),
          style: 'cancel',
        },
        {
          text: t('identity.upload.camera'),
          onPress: () => pickImage('camera'),
        },
        {
          text: t('identity.upload.gallery'),
          onPress: () => pickImage('gallery'),
        },
      ],
      { cancelable: true }
    );
  };

  const handleSubmit = async () => {
    if (!frontImageUri || !backImageUri) {
      setErrorMessage(t('identity.errors.incomplete'));
      setErrorDialogVisible(true);
      return;
    }

    setIsSubmitting(true);
    try {
      // Ensure URIs are valid
      if (!frontImageUri.startsWith('file://') && !frontImageUri.startsWith('http')) {
        throw new Error ('Invalid front image URI');
      }
      if (!backImageUri.startsWith('file://') && !backImageUri.startsWith('http')) {
        throw new Error ('Invalid back image URI');
      }

      const submissionFormData = new FormData();
      
      if (isVolunteerAssisting && assistingMemberId) {
        // For volunteer assistance mode, create FormData with both files
        const verificationData = {
          user_id: assistingMemberId,
          verification_type: documentType,
          ...parsedFormData
        };
        submissionFormData.append('data', JSON.stringify(verificationData));

        // Get file parameter names
        const fileParams = getFileParameterNames(documentType);

        // Append front image
        const frontFileName = frontImageUri.split('/').pop() || `${documentType}_front.jpg`;
        submissionFormData.append(fileParams.document, {
          uri: frontImageUri,
          name: frontFileName,
          type: 'image/jpeg',
        } as any);

        // Append back image
        const backFileName = backImageUri.split('/').pop() || `${documentType}_back.jpg`;
        if (fileParams.document2) {
          submissionFormData.append(fileParams.document2, {
            uri: backImageUri,
            name: backFileName,
            type: 'image/jpeg',
          } as any);
        }

        await submitVerificationForUserMutation.mutateAsync(submissionFormData);
      } else {
        // For regular user submission
        // Use the helper to append mapped parameters
        appendMappedParamsToFormData(submissionFormData, documentType, parsedFormData);

        // Get file parameter names
        const fileParams = getFileParameterNames(documentType);

        // Append front image
        const frontFileName = frontImageUri.split('/').pop() || `${documentType}_front.jpg`;
        submissionFormData.append(fileParams.document, {
          uri: frontImageUri,
          name: frontFileName,
          type: 'image/jpeg',
        } as any);

        // Append back image
        const backFileName = backImageUri.split('/').pop() || `${documentType}_back.jpg`;
        if (fileParams.document2) {
          submissionFormData.append(fileParams.document2, {
            uri: backImageUri,
            name: backFileName,
            type: 'image/jpeg',
          } as any);
        }
        
        await submitVerificationMutation.mutateAsync(submissionFormData);
      }
      
      await refetchVerifications();
      setSuccessDialogVisible(true);
    } catch (error: any) {
      console.error('Error submitting identity verification:', error);
      let specificMessage = t('identity.errors.upload');
       if (error.response && error.response.data && error.response.data.error) {
        const errorCode = error.response.data.error;
        const translationKey = `identity.errors.api.${errorCode.toLowerCase()}`;
        specificMessage = t(translationKey, { defaultValue: specificMessage });
      }
      setErrorMessage(specificMessage);
      setErrorDialogVisible(true);
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleSuccessConfirm = () => {
    // Close the dialog first, then navigate with a delay to prevent UI collapse
    setSuccessDialogVisible(false);
    
    // Add a small delay to ensure the dialog is fully closed before navigation
    setTimeout(() => {
      // Clear entire navigation stack and go directly to profile tab
      router.dismissAll();
      router.replace('/tabs/profile');
    }, 400);
  };

  return (
    <>
      <Stack.Screen options={{
        headerTitle: t('identity.steps.back.title'),
      }} />
      <SafeAreaView style={styles.container}>
        <StatusBar style="dark" />
        
        <ScrollView style={styles.scrollView} contentContainerStyle={styles.scrollContent}>
          <View style={styles.content}>
            <View style={styles.section}>
              <Text style={styles.sectionTitle}>{getTitle()}</Text>
              <Text style={styles.sectionDescription}>{t('identity.steps.back.description')}</Text>
              
              <View style={styles.sampleImageContainer}>
                <Image 
                  source={getSampleImage()} 
                  style={styles.sampleImage}
                  contentFit="contain"
                />
              </View>
              <Text style={styles.sampleCaption}>{getSampleCaption()}</Text>

              <TouchableOpacity 
                style={[styles.uploadButton, backImageUri && styles.uploadButtonWithImage]} 
                onPress={showUploadOptions}
              >
                {backImageUri ? (
                  <Image source={{ uri: backImageUri }} style={styles.previewImage} contentFit="cover" />
                ) : (
                  <>
                    <MaterialCommunityIcons name="camera-plus-outline" size={48} color={theme.colors.primary} />
                    <Text style={styles.uploadButtonText}>{t('identity.upload.button')}</Text>
                  </>
                )}
              </TouchableOpacity>
            </View>
          </View>
        </ScrollView>

        <View style={styles.footer}>
          <TouchableOpacity 
            style={[styles.submitButton, (!backImageUri || isSubmitting) && styles.submitButtonDisabled]}
            onPress={handleSubmit}
            disabled={!backImageUri || isSubmitting}
          >
            <Text style={styles.submitButtonText}>
              {isSubmitting ? t('common.loading') : t('identity.steps.button')}
            </Text>
          </TouchableOpacity>
        </View>

        {/* Success Dialog */}
        <CustomDialog
          visible={successDialogVisible}
          title={t('identity.steps.back.success.title')}
          message={t('identity.steps.back.success.message')}
          confirmText={t('common.ok')}
          onConfirm={handleSuccessConfirm}
          type="success"
        />

        {/* Error Dialog */}
        <CustomDialog
          visible={errorDialogVisible}
          title={t('common.error')}
          message={errorMessage}
          confirmText={t('common.ok')}
          onConfirm={() => setErrorDialogVisible(false)}
          type="error"
        />
      </SafeAreaView>
    </>
  );
}