import React, { useState } from 'react';
import { View, Text, StyleSheet, TouchableOpacity, ScrollView, Alert, Platform, SafeAreaView } from 'react-native';
import { useTranslation } from 'react-i18next';
import MaterialCommunityIcons from '@expo/vector-icons/MaterialCommunityIcons';
import { useRouter, useLocalSearchParams, Stack } from 'expo-router';
import * as ImagePicker from 'expo-image-picker';
import { StatusBar } from 'expo-status-bar';
import { appStyleStore } from 'stores/app_style_store';
import * as FileSystem from 'expo-file-system';
import { CustomDialog } from '@/common_modules/CustomDialog';
import { VerificationTypeEnum } from 'types/enums';
import { useSubmitVerification, useFetchUserVerifications, useSubmitVerificationForUser } from '@/api/user_services';
import { Image } from 'expo-image';
import { createTheme } from 'theme/index';
import { 
  appendMappedParamsToFormData, 
  getFileParameterNames, 
  getSingleImageSubmissionTypes 
} from './verificationParameterMapper';

const MAX_FILE_SIZE = 10 * 1024 * 1024; // 10MB in bytes

export default function IdentityFrontScreen() {
  const { t } = useTranslation();
  const theme = appStyleStore(state => state.theme || createTheme('red'));
  const router = useRouter();
  const params = useLocalSearchParams();
  
  const documentType = (params.documentType as VerificationTypeEnum) || VerificationTypeEnum.HkIDCard;
  const formDataString = params.formData as string | undefined;
  const parsedFormData = formDataString ? JSON.parse(formDataString) : {};
  
  // Check if we're in volunteer assistance mode
  const assistingMemberId = params.assistingMemberId as string;
  const isVolunteerAssisting = params.isVolunteerAssisting === 'true';
  
  const { refetch: refetchVerifications } = useFetchUserVerifications();
  const submitVerificationMutation = useSubmitVerification();
  const submitVerificationForUserMutation = useSubmitVerificationForUser();

  const [frontImage, setFrontImage] = useState<string | null>(null);
  const [errorDialogVisible, setErrorDialogVisible] = useState(false);
  const [errorMessage, setErrorMessage] = useState('');
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [successDialogVisible, setSuccessDialogVisible] = useState(false);

  const singleImageSubmissionTypes = getSingleImageSubmissionTypes();
  const needsBackImage = !singleImageSubmissionTypes.includes(documentType);

  const getSampleImage = () => {
    switch (documentType) {
      case VerificationTypeEnum.MainlandChinaIDCard:
        return require('@/assets/verification-samples/mainland-id-front-sample.png');
      case VerificationTypeEnum.MainlandTravelPermit:
        return require('@/assets/verification-samples/mainland-travel-permit-front-sample.jpg');
      case VerificationTypeEnum.Passport:
        return require('@/assets/verification-samples/passport-sample.jpg');
      case VerificationTypeEnum.HkYouthPlus:
        return require('@/assets/verification-samples/hkyouth-plus-sample.png');
      case VerificationTypeEnum.AddressProof:
        return require('@/assets/verification-samples/address-proof-sample.png');
      case VerificationTypeEnum.StudentID:
        return require('@/assets/verification-samples/student-id-front-sample.png');
      case VerificationTypeEnum.HkIDCard:
      default:
        return require('@/assets/verification-samples/id-card-front-sample.png');
    }
  };

  const getTitle = () => {
    return t(`identity.steps.front.${documentType}.title`, t('identity.steps.front.default.title'));
  };
  const getSampleCaption = () => {
    // Get the appropriate sample caption based on document type
   return t(`identity.steps.front.${documentType}.sample`, t('identity.steps.front.default.sample'));
  };
  
  const getAspectRatio = (): [number, number] => {
    switch (documentType) {
      case VerificationTypeEnum.Passport: return [3, 4]; // Taller for passport
      case VerificationTypeEnum.HkIDCard:
      case VerificationTypeEnum.MainlandChinaIDCard:
      case VerificationTypeEnum.MainlandTravelPermit:
      case VerificationTypeEnum.StudentID: 
          return [3, 2]; // Standard ID card like aspect ratio
      case VerificationTypeEnum.HkYouthPlus:
      case VerificationTypeEnum.AddressProof: 
        return [4, 3]; // More flexible, like a document photo
      default: return [4, 3];
    }
  };

  const styles = StyleSheet.create({
    container: { flex: 1, backgroundColor: theme.system.background },
    scrollView: { flex: 1 },
    scrollContent: { flexGrow: 1 },
    content: { padding: 24, flex: 1, justifyContent: 'space-between' }, // Allow footer to stick
    section: { flex: 1 }, // Allow content to take available space
    sectionTitle: { fontSize: 20, fontWeight: '600', color: theme.system.text, marginBottom: 8, textAlign: 'center' },
    sectionDescription: { fontSize: 15, color: theme.system.secondaryText, marginBottom: 16, textAlign: 'center' },
    sampleImageContainer: { alignItems: 'center', marginBottom: 16 },
    sampleImage: { width: '80%', height: undefined, aspectRatio: getAspectRatio()[0] / getAspectRatio()[1], borderRadius: 8, resizeMode: 'contain' },
    sampleCaption: { fontSize: 14, color: theme.system.secondaryText, textAlign: 'center', marginTop: -16, marginBottom: 24 },
    uploadButton: { 
        height: 200, // Adjusted for better preview
        borderWidth: 1, 
        borderColor: 'gray', 
        borderRadius: 12, 
        borderStyle: 'dashed', 
        alignItems: 'center', 
        justifyContent: 'center', 
        backgroundColor: '#F5f5f5',
        overflow: 'hidden' // Ensure preview image respects border radius
    },
    uploadButtonWithImage: { borderStyle: 'solid', backgroundColor: 'transparent' }, // No background if image present
    uploadButtonText: { marginTop: 8, fontSize: 15, color: theme.colors.primary },
    previewImage: { width: '100%', height: '100%', borderRadius: 11 }, // borderRadius slightly less than button
    footer: { 
      paddingHorizontal: 24, 
      paddingVertical: 16,
      borderTopWidth: 1,
      borderTopColor: theme.system.border,
      backgroundColor: theme.system.background,
    },
    nextButton: { 
      flexDirection: 'row', 
      alignItems: 'center', 
      justifyContent: 'center', 
      backgroundColor: theme.colors.primary, 
      paddingVertical: 16, 
      borderRadius: 12,
      gap: 8 
    },
    nextButtonDisabled: { 
      backgroundColor: theme.colors.primaryDisabled || '#CCCCCC',
    },
    nextButtonText: { fontSize: 17, fontWeight: '600', color: '#FFFFFF' },
  });

  const requestPermissions = async () => {
    if (Platform.OS !== 'web') {
      const { status: cameraStatus } = await ImagePicker.requestCameraPermissionsAsync();
      const { status: libraryStatus } = await ImagePicker.requestMediaLibraryPermissionsAsync();
      
      if (cameraStatus !== 'granted') {
        Alert.alert(t('common.error'), t('identity.permissions.camera'), [{ text: t('common.ok') }]);
        return false;
      }
      
      if (libraryStatus !== 'granted') {
        Alert.alert(t('common.error'), t('identity.permissions.gallery'), [{ text: t('common.ok') }]);
        return false;
      }
    }
    return true;
  };

  const pickImage = async (method: 'camera' | 'gallery') => {
    const hasPermissions = await requestPermissions();
    if (!hasPermissions) return;

    try {
      const options: ImagePicker.ImagePickerOptions = {
        mediaTypes: ['images'],
        allowsEditing: true,
        aspect: getAspectRatio(), 
        quality: 0.7, // Slightly reduced quality for performance
      };

      const result = method === 'camera'
        ? await ImagePicker.launchCameraAsync(options)
        : await ImagePicker.launchImageLibraryAsync(options);

      if (!result.canceled && result.assets && result.assets[0]) {
        const asset = result.assets[0];
        const originalUri = asset.uri;

        if (asset.fileSize && asset.fileSize > MAX_FILE_SIZE) {
          setErrorMessage(t('identity.errors.fileTooLarge', { maxSize: '10MB' }));
          setErrorDialogVisible(true);
          return;
        }

        let stableUri = originalUri;
        // Immediately copy to a stable location on Android if it's a file URI
        if (Platform.OS === 'android' && originalUri.startsWith('file://')) {
          const fileName = originalUri.split('/').pop() || `pickedImage_${Date.now()}.jpg`;
          const permDir = FileSystem.cacheDirectory + 'AppPickedImages/'; 
          await FileSystem.makeDirectoryAsync(permDir, { intermediates: true });
          stableUri = permDir + fileName;
          try {
            await FileSystem.copyAsync({ from: originalUri, to: stableUri });
            console.log('Front image copied to stable URI:', stableUri);
          } catch (copyError) {
            console.error('Error copying picked front image immediately:', copyError);
            setErrorMessage(t('identity.errors.upload') + ' (Copy Error Front)');
            setErrorDialogVisible(true);
            // If immediate copy fails, fall back to original URI. 
            // This might still lead to issues later, but it's better than crashing here.
            stableUri = originalUri; 
          }
        }
        setFrontImage(stableUri); // Store URI (potentially of the copied file)
      }
    } catch (error: any) {
      console.error("Image picking/processing error:", error);
      setErrorMessage(method === 'camera' ? t('identity.errors.capture') : t('identity.errors.upload'));
      setErrorDialogVisible(true);
    }
  };

  const showUploadOptions = () => {
    Alert.alert(
      t('identity.upload.title'), 
      '', 
      [
        { text: t('common.cancel'), style: 'cancel' },
        { text: t('identity.upload.camera'), onPress: () => pickImage('camera') },
        { text: t('identity.upload.gallery'), onPress: () => pickImage('gallery') },
      ], { cancelable: true }
    );
  };

  const handleNext = async () => {
    if (!frontImage) {
      setErrorMessage(t('identity.steps.front.error.noImage'));
      setErrorDialogVisible(true);
      return;
    }

    setIsSubmitting(true);
    if (singleImageSubmissionTypes.includes(documentType)) {
      // Submit directly for single image types
      try {
        // Create FormData
        const formDataSubmit = new FormData();
        
        if (isVolunteerAssisting && assistingMemberId) {
          // For volunteer assistance mode, append user_id and verification_type in data field
          const verificationData = {
            user_id: assistingMemberId,
            verification_type: documentType,
            ...parsedFormData
          };
          formDataSubmit.append('data', JSON.stringify(verificationData));
          
          // Get file parameter names and append the file with correct parameter name
          const fileParams = getFileParameterNames(documentType);
          const fileName = frontImage.split('/').pop() || `${documentType}_front.jpg`;
          formDataSubmit.append(fileParams.document, {
              uri: frontImage,
              name: fileName,
              type: 'image/jpeg',
          } as any);

          await submitVerificationForUserMutation.mutateAsync(formDataSubmit);
        } else {
          // For regular user submission
          // Use the helper to append mapped parameters
          appendMappedParamsToFormData(formDataSubmit, documentType, parsedFormData);

          // Get file parameter names
          const fileParams = getFileParameterNames(documentType);

          // Append the front image file
          const fileName = frontImage.split('/').pop() || `${documentType}_front.jpg`;
          formDataSubmit.append(fileParams.document, {
              uri: frontImage,
              name: fileName,
              type: 'image/jpeg',
          } as any);

          await submitVerificationMutation.mutateAsync(formDataSubmit);
        }
        
        await refetchVerifications();
        setSuccessDialogVisible(true);
      } catch (error: any) {
        console.error('Verification submission error (front screen):', error);
        let specificMessage = t('programApplications.modal.messages.submitError');
        if (error.response && error.response.data && error.response.data.error) {
            const errorCode = error.response.data.error;
            const translationKey = `identity.errors.api.${errorCode.toLowerCase()}`;
            specificMessage = t(translationKey, { defaultValue: specificMessage });
        }
        setErrorMessage(specificMessage);
        setErrorDialogVisible(true);
      } finally {
        setIsSubmitting(false);
      }
    } else {
      // For other ID types, navigate to back screen, passing URI and volunteer assistance params
      const updatedFormData = { ...parsedFormData }; // frontImage is passed separately
      
      // Prepare navigation params
      const navigationParams: any = { 
        formData: JSON.stringify(updatedFormData),
        frontImageUri: frontImage,
        documentType: documentType 
      };
      
      // Add volunteer assistance params if present
      const assistingMemberId = params.assistingMemberId as string;
      const isVolunteerAssisting = params.isVolunteerAssisting === 'true';
      
      if (isVolunteerAssisting && assistingMemberId) {
        navigationParams.assistingMemberId = assistingMemberId;
        navigationParams.isVolunteerAssisting = 'true';
      }

      router.push({
        pathname: '/user-profile/identification/IdentityBackScreen',
        params: navigationParams,
      });
      setIsSubmitting(false);
    }
  };

  const handleSuccessConfirm = () => {
    // Close the dialog first, then navigate with a delay to prevent UI collapse
    setSuccessDialogVisible(false);
    
    // Add a small delay to ensure the dialog is fully closed before navigation
    setTimeout(() => {
      // Clear entire navigation stack and go directly to profile tab
      router.dismissAll();
      router.replace('/tabs/profile');
    }, 400);
  };

  return (
    <>
      <Stack.Screen options={{
        headerTitle: t('identity.steps.front.title'),
      }} />
      <SafeAreaView style={styles.container}>
        <StatusBar style="dark" />
        
        <ScrollView style={styles.scrollView} contentContainerStyle={styles.scrollContent}>
          <View style={styles.content}>
            <View style={styles.section}>
              <Text style={styles.sectionTitle}>{getTitle()}</Text>
              <Text style={styles.sectionDescription}>{t('identity.steps.front.description')}</Text>
              
              <View style={styles.sampleImageContainer}>
                  <Image 
                  source={getSampleImage()} 
                  style={styles.sampleImage}
                  contentFit="contain"
                  />
              </View>
              <Text style={styles.sampleCaption}>{getSampleCaption()}</Text>

              <TouchableOpacity 
                style={[
                  styles.uploadButton, 
                  frontImage && styles.uploadButtonWithImage,
                ]} 
                onPress={showUploadOptions}
              >
                {frontImage ? (
                  <Image 
                    source={{ uri: frontImage }} 
                    style={styles.previewImage} 
                    contentFit={getAspectRatio()[0] / getAspectRatio()[1] > 1.4 ? "cover" : "contain"} // cover for wider, contain for taller
                  />
                ) : (
                  <>
                    <MaterialCommunityIcons name="camera-plus-outline" size={48} color={theme.colors.primary} />
                    <Text style={styles.uploadButtonText}>{t('identity.upload.button')}</Text>
                  </>
                )}
              </TouchableOpacity>
            </View>
          </View>
        </ScrollView>

        <View style={styles.footer}>
          <TouchableOpacity 
            style={[styles.nextButton, (!frontImage || isSubmitting) && styles.nextButtonDisabled]}
            onPress={handleNext}
            disabled={!frontImage || isSubmitting}
          >
            <Text style={styles.nextButtonText}>
              {isSubmitting ? t('common.loading') : 
                  (singleImageSubmissionTypes.includes(documentType) ? t('common.submit') : t('common.continue'))}
            </Text>
            {(!singleImageSubmissionTypes.includes(documentType) && !isSubmitting) && 
              <MaterialCommunityIcons name="arrow-right" size={20} color="#FFFFFF" />}
          </TouchableOpacity>
        </View>

        <CustomDialog
          visible={errorDialogVisible}
          title={t('common.error')}
          message={errorMessage}
          confirmText={t('common.ok')}
          onConfirm={() => setErrorDialogVisible(false)}
          type="error"
        />
        <CustomDialog
          visible={successDialogVisible}
          title={t('identity.success.title')}
          message={t('identity.success.message')} // This message might need to be dynamic based on type
          confirmText={t('common.ok')}
          onConfirm={handleSuccessConfirm}
          type="success"
        />
      </SafeAreaView>
    </>
  );
}