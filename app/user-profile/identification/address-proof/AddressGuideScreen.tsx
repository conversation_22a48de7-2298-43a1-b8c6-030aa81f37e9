import React from 'react';
import { View, Text, StyleSheet, ScrollView, TouchableOpacity, SafeAreaView } from 'react-native';
import { useTranslation } from 'react-i18next';
import MaterialCommunityIcons from '@expo/vector-icons/MaterialCommunityIcons';
import { useRouter, Stack } from 'expo-router';
import { StatusBar } from 'expo-status-bar';
import { appStyleStore } from 'stores/app_style_store';

export default function AddressGuideScreen() {
  const { t } = useTranslation();
  const router = useRouter();
  const theme = appStyleStore(state => state.theme );

  const handleStart = () => {
    router.push('/user-profile/identification/addressProof/AddressVerificationScreen');
  };

  return (
    <SafeAreaView style={[styles.container, { backgroundColor: theme.colors.background }]}>
      <StatusBar style="dark" />
      <ScrollView 
        style={styles.scrollView} 
        contentContainerStyle={styles.scrollContent}
        showsVerticalScrollIndicator={false}
      >
        <View style={styles.content}>
          {/* Cover Section */}
          <View style={styles.coverSection}>
            <Text style={[styles.subtitle]}>{t('identity.address.description')}</Text>
          </View>

          {/* Document Types Section */}
          <View style={styles.section}>
            <View style={styles.sectionHeader}>
              <MaterialCommunityIcons 
                name="file-document-multiple-outline" 
                size={20} 
                color={theme.colors.primary} 
              />
              <Text style={styles.sectionTitle}>{t('identity.address.guide.documents.title')}</Text>
            </View>
            <View style={styles.bulletPoints}>
              {t('identity.address.guide.documents.description').split('\n').map((point, index) => (
                <View key={index} style={styles.bulletPoint}>
                  <Text style={styles.bullet}>•</Text>
                  <Text style={styles.bulletText}>{point.replace('• ', '')}</Text>
                </View>
              ))}
            </View>
          </View>

          {/* Requirements Section */}
          <View style={styles.section}>
            <View style={styles.sectionHeader}>
              <MaterialCommunityIcons 
                name="checkbox-marked-circle-outline" 
                size={20} 
                color={theme.colors.primary} 
              />
              <Text style={styles.sectionTitle}>{t('identity.address.guide.validity.title')}</Text>
            </View>
            <View style={styles.bulletPoints}>
              {t('identity.address.guide.validity.description').split('\n').map((point, index) => (
                <View key={index} style={styles.bulletPoint}>
                  <Text style={styles.bullet}>•</Text>
                  <Text style={styles.bulletText}>{point.replace('• ', '')}</Text>
                </View>
              ))}
            </View>
          </View>

          {/* File Requirements Section */}
          <View style={styles.section}>
            <View style={styles.sectionHeader}>
              <MaterialCommunityIcons 
                name="file-check-outline" 
                size={20} 
                color={theme.colors.primary} 
              />
              <Text style={styles.sectionTitle}>{t('identity.address.guide.requirements.title')}</Text>
            </View>
            <View style={styles.bulletPoints}>
              {t('identity.address.guide.requirements.description').split('\n').map((point, index) => (
                <View key={index} style={styles.bulletPoint}>
                  <Text style={styles.bullet}>•</Text>
                  <Text style={styles.bulletText}>{point.replace('• ', '')}</Text>
                </View>
              ))}
            </View>
          </View>

          <TouchableOpacity 
            style={[styles.startButton, { backgroundColor: theme.colors.primary }]} 
            onPress={handleStart}
          >
            <Text style={styles.startButtonText}>{t('identity.guide.startButton')}</Text>
            <MaterialCommunityIcons name="arrow-right" size={20} color="#FFFFFF" />
          </TouchableOpacity>
        </View>
      </ScrollView>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  scrollView: {
    flex: 1,
  },
  scrollContent: {
    flexGrow: 1,
  },
  content: {
    flex: 1,
    padding: 24,
  },
  coverSection: {
    marginBottom: 24,
  },
  subtitle: {
    fontSize: 15,
    lineHeight: 20,
  },
  section: {
    marginBottom: 24,
  },
  sectionHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 12,
  },
  sectionTitle: {
    fontSize: 17,
    fontWeight: '600',
    marginLeft: 8,
  },
  bulletPoints: {
    marginLeft: 4,
  },
  bulletPoint: {
    flexDirection: 'row',
    alignItems: 'flex-start',
    marginBottom: 8,
  },
  bullet: {
    fontSize: 22,
    marginRight: 8,
    marginTop: -4,
  },
  bulletText: {
    flex: 1,
    fontSize: 15,
    lineHeight: 20,
  },
  startButton: {
    paddingVertical: 16,
    borderRadius: 12,
    alignItems: 'center',
    flexDirection: 'row',
    justifyContent: 'center',
    gap: 8,
  },
  startButtonText: {
    fontSize: 17,
    fontWeight: '600',
    color: '#FFFFFF',
  },
});