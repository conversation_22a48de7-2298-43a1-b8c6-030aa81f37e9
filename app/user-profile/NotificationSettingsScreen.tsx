import React, { useEffect, useState } from 'react';
import { View, Text, StyleSheet, ScrollView } from 'react-native';
import { Switch, List, Divider } from 'react-native-paper';
import { useTranslation } from 'react-i18next';
import { Stack } from 'expo-router';
import { appStyleStore } from 'stores/app_style_store';
import { userProfileStore } from 'stores/user_store';
import { authenticationStore } from 'stores/authentication_store';
import { useFetchUserProfile, useUpdateUserProfile } from '@/api/user_services';
import { createTheme } from 'theme/index';

interface NotificationPreferences {
  appNotifications: boolean;
  whatsappNotifications: boolean;
}

export function NotificationSettingsScreen() {
  const { t } = useTranslation();
  const theme = appStyleStore(state => state.theme || createTheme('red'));
  const isAuthenticated = authenticationStore(state => state.isAuthenticated);
  const userProfile = userProfileStore(state => state.profile);
  
  // Fetch user profile data
  useFetchUserProfile();
  
  // Update user profile mutation
  const updateUserProfileMutation = useUpdateUserProfile();
  
  const [notificationPreferences, setNotificationPreferences] = useState<NotificationPreferences>({
    appNotifications: false,
    whatsappNotifications: false,
  });

  useEffect(() => {
    if (userProfile) {
      setNotificationPreferences({
        appNotifications: userProfile.enable_app_notifications,
        whatsappNotifications: userProfile.enable_whatsapp_notifications
      });
    }
  }, [userProfile]);

  const updateNotificationPreferences = async (update: Partial<NotificationPreferences>) => {
    if (!isAuthenticated) return;
    
    const newPreferences = { ...notificationPreferences, ...update };

    try {
      await updateUserProfileMutation.mutateAsync({
        notification_settings: {
          enable_app_notifications: newPreferences.appNotifications,
          enable_whatsapp_notifications: newPreferences.whatsappNotifications
        }
      });
      setNotificationPreferences(newPreferences);
    } catch (error) {
      console.error('Error updating notification preferences:', error);
      // Revert on error
      setNotificationPreferences(notificationPreferences);
    }
  };

  const handleToggle = async (key: keyof NotificationPreferences, value: boolean) => {
    // For WhatsApp notifications, implement mutually exclusive behavior with app notifications
    if (key === 'whatsappNotifications' && value === true) {
      // If turning on WhatsApp, turn off app notifications
      await updateNotificationPreferences({ 
        whatsappNotifications: true, 
        appNotifications: false 
      });
    } else if (key === 'appNotifications' && value === true) {
      // If turning on app notifications, turn off WhatsApp
      await updateNotificationPreferences({ 
        appNotifications: true, 
        whatsappNotifications: false 
      });
    } else {
      // For turning off notifications, proceed normally
      await updateNotificationPreferences({ [key]: value });
    }
  };

  const renderSwitch = (
    value: boolean,
    onValueChange: (value: boolean) => void,
    disabled?: boolean
  ) => (
    <Switch
      value={value}
      onValueChange={onValueChange}
      color={theme.colors.primary}
      disabled={disabled || updateUserProfileMutation.isPending}
      style={styles.switch}
    />
  );

  const renderListItem = (
    title: string,
    description: string,
    value: boolean,
    onValueChange: (value: boolean) => void,
    disabled?: boolean
  ) => (
    <List.Item
      title={<Text style={[styles.listItemTitle, { color: theme.system.text }]}>{title}</Text>}
      description={<Text style={[styles.listItemDescription, { color: theme.system.secondaryText }]}>{description}</Text>}
      right={() => renderSwitch(value, onValueChange, disabled)}
      style={[styles.listItem, { backgroundColor: theme.colors.background }]}
    />
  );

  return (
    <>
      <Stack.Screen options={{
        headerTitle: t('notifications.title'),
        headerStyle: {
          backgroundColor: theme.colors.background,
        },
      }} />
      <View style={[styles.safeArea, { backgroundColor: theme.colors.background }]}>
        <ScrollView
          style={[styles.container, { backgroundColor: theme.colors.background }]}
          contentContainerStyle={styles.contentContainer}
        >
          {/* Notification Channels */}
          <View style={styles.section}>
            <Text style={[styles.sectionTitle, { color: theme.system.secondaryText }]}>
              {t('notifications.channels.title')}
            </Text>
            {renderListItem(
              t('notifications.channels.app'),
              t('notifications.channels.appDescription'),
              notificationPreferences.appNotifications,
              (value) => handleToggle('appNotifications', value)
            )}
            {renderListItem(
              t('notifications.channels.whatsapp'),
              t('notifications.channels.whatsappDescription'),
              notificationPreferences.whatsappNotifications,
              (value) => handleToggle('whatsappNotifications', value)
            )}
          </View>
        </ScrollView>
      </View>
    </>
  );
}

const styles = StyleSheet.create({
  safeArea: {
    flex: 1,
  },
  container: {
    flex: 1,
  },
  contentContainer: {
    paddingBottom: 24,
  },
  section: {
    paddingTop: 16,
    paddingBottom: 8,
  },
  sectionTitle: {
    fontSize: 15,
    fontWeight: '600',
    marginLeft: 16,
    marginBottom: 8,
    letterSpacing: 0.3,
    lineHeight: 20,
  },
  listItem: {
    paddingVertical: 12,
    paddingHorizontal: 0,
    minHeight: 76,
  },
  listItemTitle: {
    fontSize: 16,
    fontWeight: '500',
    lineHeight: 22,
    letterSpacing: 0.2,
    marginBottom: 4,
    paddingHorizontal: 16,
  },
  listItemDescription: {
    fontSize: 14,
    lineHeight: 20,
    letterSpacing: 0.1,
    paddingHorizontal: 16,
  },
  divider: {
    height: StyleSheet.hairlineWidth,
  },
  switch: {
    marginRight: 16,
  },
});

export default NotificationSettingsScreen;