import React, { useState, useEffect } from 'react';
import { View, Text, StyleSheet, ScrollView, TouchableOpacity, TextInput, KeyboardAvoidingView, Platform, Alert } from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { But<PERSON>, ActivityIndicator } from 'react-native-paper';
import MaterialCommunityIcons from '@expo/vector-icons/MaterialCommunityIcons';
import Ionicons from '@expo/vector-icons/Ionicons';
import { useRouter, Stack } from 'expo-router';
import { useTranslation } from 'react-i18next';
import { appStyleStore } from 'stores/app_style_store';
import { createTheme } from 'theme/index';
import { CustomDialog } from '@/common_modules/CustomDialog';
import { 
  useInitiateAccountDeletion, 
  useConfirmAccountDeletion, 
  useFetchAccountDeletionStatus,
  useCancelAccountDeletion 
} from '@/api/user_services';

// Utility function to get more specific error messages
const getErrorMessage = (error: any, operation: string, t: any): string => {
  if (error?.response?.data?.message) {
    return error.response.data.message;
  }
  
  const status = error?.response?.status;
  switch (status) {
    case 400:
      return t('profile.deleteAccount.badRequest');
    case 401:
      return t('profile.deleteAccount.unauthorized');
    case 403:
      return t('profile.deleteAccount.forbidden');
    case 404:
      return t('profile.deleteAccount.notFound');
    case 409:
      return t('profile.deleteAccount.conflict');
    case 429:
      return t('profile.deleteAccount.tooManyRequests');
    case 500:
      return t('profile.deleteAccount.serverError');
    default:
      return `${t('profile.deleteAccount.errorDuring')} ${operation}. ${t('common.tryAgain')}`;
  }
};

export default function DeleteAccountScreen() {
  const { t } = useTranslation();
  const router = useRouter();
  const storedTheme = appStyleStore(state => state.theme);
  const theme = storedTheme || createTheme('red');

  const [step, setStep] = useState<'initiate' | 'confirm' | 'scheduled'>('initiate');
  const [otpCode, setOtpCode] = useState('');
  const [deleteReason, setDeleteReason] = useState('');
  const [showSuccessDialog, setShowSuccessDialog] = useState(false);
  const [showErrorDialog, setShowErrorDialog] = useState(false);
  const [errorMessage, setErrorMessage] = useState('');

  // API hooks
  const initiateDeletion = useInitiateAccountDeletion();
  const confirmDeletion = useConfirmAccountDeletion();
  const cancelDeletion = useCancelAccountDeletion();
  const { data: deletionStatus, refetch: refetchStatus } = useFetchAccountDeletionStatus();

  // Check deletion status on mount
  useEffect(() => {
    if (deletionStatus) {
      if (deletionStatus.status === 'pending_confirmation') {
        setStep('confirm');
      } else if (deletionStatus.status === 'scheduled') {
        setStep('scheduled');
      }
    }
  }, [deletionStatus]);

  const handleInitiate = async () => {
    try {
      await initiateDeletion.mutateAsync({ reason: deleteReason });
      setStep('confirm');
      Alert.alert(
        t('profile.deleteAccount.otpSent'),
        t('profile.deleteAccount.otpSentMessage'),
        [{ text: t('common.ok') }]
      );
    } catch (error: any) {
      setErrorMessage(getErrorMessage(error, t('profile.deleteAccount.initiation'), t));
      setShowErrorDialog(true);
    }
  };

  const handleConfirm = async () => {
    // Validate OTP: exactly 6 numeric digits
    const otpRegex = /^\d{6}$/;
    if (!otpRegex.test(otpCode)) {
      Alert.alert(t('common.error'), t('profile.deleteAccount.invalidOtp'));
      return;
    }

    try {
      await confirmDeletion.mutateAsync({ code: otpCode });
      setStep('scheduled');
      setShowSuccessDialog(true);
      refetchStatus();
    } catch (error: any) {
      setErrorMessage(getErrorMessage(error, t('profile.deleteAccount.confirmation'), t));
      setShowErrorDialog(true);
    }
  };

  const handleCancel = async () => {
    Alert.alert(
      t('profile.deleteAccount.cancelTitle'),
      t('profile.deleteAccount.cancelMessage'),
      [
        { text: t('common.no'), style: 'cancel' },
        {
          text: t('common.yes'),
          onPress: async () => {
            try {
              await cancelDeletion.mutateAsync();
              Alert.alert(
                t('common.success'),
                t('profile.deleteAccount.cancelSuccess'),
                [{ text: t('common.ok'), onPress: () => router.back() }]
              );
            } catch (error: any) {
              setErrorMessage(getErrorMessage(error, t('profile.deleteAccount.cancellation'), t));
              setShowErrorDialog(true);
            }
          },
        },
      ]
    );
  };

  const renderInitiateStep = () => (
    <View style={styles.content}>
      <View style={styles.header}>
        <View style={[styles.iconContainer, { borderColor: theme.colors.error }]}>
          <MaterialCommunityIcons name="delete-empty" size={54} color={theme.colors.error} />
        </View>
        <Text style={[styles.title, { color: theme.system.text }]}>
          {t('profile.deleteAccount.title')}
        </Text>
      </View>

      <View style={styles.warningContainer}>
        <Text style={[styles.warningTitle, { color: theme.colors.error }]}>
          {t('profile.deleteAccount.warningTitle')}
        </Text>
        <Text style={[styles.warningText, { color: theme.system.text }]}>
          {t('profile.deleteAccount.warningText')}
        </Text>
      </View>

      <View style={styles.infoContainer}>
        <Text style={[styles.infoTitle, { color: theme.system.text }]}>
          {t('profile.deleteAccount.whatHappens')}
        </Text>
        <View style={styles.infoItem}>
          <Ionicons name="checkmark-circle" size={20} color={theme.colors.primary} />
          <Text style={[styles.infoText, { color: theme.system.text }]}>
            {t('profile.deleteAccount.info1')}
          </Text>
        </View>
        <View style={styles.infoItem}>
          <Ionicons name="checkmark-circle" size={20} color={theme.colors.primary} />
          <Text style={[styles.infoText, { color: theme.system.text }]}>
            {t('profile.deleteAccount.info2')}
          </Text>
        </View>
        <View style={styles.infoItem}>
          <Ionicons name="checkmark-circle" size={20} color={theme.colors.primary} />
          <Text style={[styles.infoText, { color: theme.system.text }]}>
            {t('profile.deleteAccount.info3')}
          </Text>
        </View>
      </View>

      <View style={styles.reasonContainer}>
        <Text style={[styles.reasonLabel, { color: theme.system.text }]}>
          {t('profile.deleteAccount.reasonLabel')}
        </Text>
        <TextInput
          style={[styles.reasonInput, { 
            borderColor: theme.system.border,
            color: theme.system.text,
            backgroundColor: theme.system.background 
          }]}
          placeholder={t('profile.deleteAccount.reasonPlaceholder')}
          placeholderTextColor={theme.system.textLight}
          multiline
          numberOfLines={3}
          value={deleteReason}
          onChangeText={setDeleteReason}
        />
      </View>

      <View style={styles.buttonContainer}>
        <Button
          mode="outlined"
          onPress={() => router.back()}
          style={[styles.button, styles.cancelButton]}
          labelStyle={{ color: theme.system.text }}
        >
          {t('common.cancel')}
        </Button>
        <Button
          mode="contained"
          onPress={handleInitiate}
          style={[styles.button, { backgroundColor: theme.colors.error }]}
          loading={initiateDeletion.isPending}
          disabled={initiateDeletion.isPending}
        >
          {t('profile.deleteAccount.continue')}
        </Button>
      </View>
    </View>
  );

  const renderConfirmStep = () => (
    <View style={styles.content}>
      <View style={styles.header}>
        <View style={[styles.iconContainer, { borderColor: theme.colors.primary }]}>
          <MaterialCommunityIcons name="shield-lock" size={54} color={theme.colors.primary} />
        </View>
        <Text style={[styles.title, { color: theme.system.text }]}>
          {t('profile.deleteAccount.confirmTitle')}
        </Text>
      </View>

      <Text style={[styles.description, { color: theme.system.text }]}>
        {t('profile.deleteAccount.confirmDescription')}
      </Text>

      <View style={styles.otpContainer}>
        <TextInput
          style={[styles.otpInput, { 
            borderColor: theme.system.border,
            color: theme.system.text,
            backgroundColor: theme.system.background 
          }]}
          placeholder="000000"
          placeholderTextColor={theme.system.textLight}
          keyboardType="number-pad"
          maxLength={6}
          value={otpCode}
          onChangeText={setOtpCode}
          autoFocus
        />
      </View>

      <View style={styles.buttonContainer}>
        <Button
          mode="outlined"
          onPress={() => setStep('initiate')}
          style={[styles.button, styles.cancelButton]}
          labelStyle={{ color: theme.system.text }}
        >
          {t('common.back')}
        </Button>
        <Button
          mode="contained"
          onPress={handleConfirm}
          style={[styles.button, { backgroundColor: theme.colors.error }]}
          loading={confirmDeletion.isPending}
          disabled={confirmDeletion.isPending || !/^\d{6}$/.test(otpCode)}
        >
          {t('profile.deleteAccount.confirmDelete')}
        </Button>
      </View>
    </View>
  );

  const renderScheduledStep = () => (
    <View style={styles.content}>
      <View style={styles.header}>
        <View style={[styles.iconContainer, { borderColor: theme.colors.warning }]}>
          <MaterialCommunityIcons name="clock-alert" size={54} color={theme.colors.warning} />
        </View>
        <Text style={[styles.title, { color: theme.system.text }]}>
          {t('profile.deleteAccount.scheduledTitle')}
        </Text>
      </View>

      <Text style={[styles.description, { color: theme.system.text }]}>
        {t('profile.deleteAccount.scheduledDescription')}
      </Text>

      {deletionStatus?.scheduled_for && (
        <View style={[styles.dateContainer, { backgroundColor: theme.system.backgroundLight }]}>
          <Text style={[styles.dateLabel, { color: theme.system.textLight }]}>
            {t('profile.deleteAccount.scheduledFor')}
          </Text>
          <Text style={[styles.dateText, { color: theme.system.text }]}>
            {new Date(deletionStatus.scheduled_for).toLocaleDateString()}
          </Text>
        </View>
      )}

      <View style={styles.infoContainer}>
        <Text style={[styles.infoTitle, { color: theme.system.text }]}>
          {t('profile.deleteAccount.canStillUse')}
        </Text>
        <Text style={[styles.infoText, { color: theme.system.text }]}>
          {t('profile.deleteAccount.cancelAnytime')}
        </Text>
      </View>

      <View style={styles.buttonContainer}>
        <Button
          mode="contained"
          onPress={handleCancel}
          style={[styles.button, { backgroundColor: theme.colors.primary }]}
          loading={cancelDeletion.isPending}
          disabled={cancelDeletion.isPending}
        >
          {t('profile.deleteAccount.cancelDeletion')}
        </Button>
        <Button
          mode="outlined"
          onPress={() => router.back()}
          style={[styles.button]}
          labelStyle={{ color: theme.system.text }}
        >
          {t('common.done')}
        </Button>
      </View>
    </View>
  );

  return (
    <>
      <Stack.Screen options={{
        headerTitle: t('profile.deleteAccount.title'),
      }} />

      <SafeAreaView style={[styles.safeArea, { backgroundColor: theme.system.background }]} edges={['bottom']}>
        <KeyboardAvoidingView 
          style={styles.container}
          behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
        >
          <ScrollView
            style={[styles.container, { backgroundColor: theme.system.background }]}
            contentContainerStyle={styles.contentContainer}
            keyboardShouldPersistTaps="handled"
          >
            {step === 'initiate' && renderInitiateStep()}
            {step === 'confirm' && renderConfirmStep()}
            {step === 'scheduled' && renderScheduledStep()}
          </ScrollView>
        </KeyboardAvoidingView>
      </SafeAreaView>

      <CustomDialog
        visible={showSuccessDialog}
        onDismiss={() => setShowSuccessDialog(false)}
        icon="check-circle"
        iconColor={theme.colors.primary}
        title={t('common.success')}
        message={t('profile.deleteAccount.successMessage')}
        buttons={[
          {
            text: t('common.ok'),
            onPress: () => {
              setShowSuccessDialog(false);
              router.back();
            },
          },
        ]}
      />

      <CustomDialog
        visible={showErrorDialog}
        onDismiss={() => setShowErrorDialog(false)}
        icon="alert-circle"
        iconColor={theme.colors.error}
        title={t('common.error')}
        message={errorMessage}
        buttons={[
          {
            text: t('common.ok'),
            onPress: () => setShowErrorDialog(false),
          },
        ]}
      />
    </>
  );
}

const styles = StyleSheet.create({
  safeArea: {
    flex: 1,
  },
  container: {
    flex: 1,
  },
  contentContainer: {
    flexGrow: 1,
    paddingHorizontal: 20,
    paddingBottom: 20,
  },
  content: {
    flex: 1,
  },
  header: {
    alignItems: 'center',
    marginTop: 20,
    marginBottom: 30,
  },
  iconContainer: {
    width: 100,
    height: 100,
    borderRadius: 50,
    borderWidth: 3,
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 20,
  },
  title: {
    fontSize: 24,
    fontWeight: 'bold',
  },
  description: {
    fontSize: 16,
    textAlign: 'center',
    marginHorizontal: 20,
    marginBottom: 30,
    lineHeight: 24,
  },
  warningContainer: {
    backgroundColor: 'rgba(255, 0, 0, 0.1)',
    padding: 16,
    borderRadius: 8,
    marginBottom: 20,
  },
  warningTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    marginBottom: 8,
  },
  warningText: {
    fontSize: 14,
    lineHeight: 20,
  },
  infoContainer: {
    marginBottom: 20,
  },
  infoTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    marginBottom: 12,
  },
  infoItem: {
    flexDirection: 'row',
    alignItems: 'flex-start',
    marginBottom: 8,
  },
  infoText: {
    fontSize: 14,
    marginLeft: 8,
    flex: 1,
    lineHeight: 20,
  },
  reasonContainer: {
    marginBottom: 30,
  },
  reasonLabel: {
    fontSize: 16,
    fontWeight: '600',
    marginBottom: 8,
  },
  reasonInput: {
    borderWidth: 1,
    borderRadius: 8,
    padding: 12,
    minHeight: 80,
    textAlignVertical: 'top',
  },
  otpContainer: {
    alignItems: 'center',
    marginBottom: 30,
  },
  otpInput: {
    borderWidth: 2,
    borderRadius: 8,
    padding: 16,
    fontSize: 24,
    letterSpacing: 8,
    width: 200,
    textAlign: 'center',
    fontWeight: 'bold',
  },
  dateContainer: {
    padding: 16,
    borderRadius: 8,
    marginBottom: 20,
    alignItems: 'center',
  },
  dateLabel: {
    fontSize: 14,
    marginBottom: 4,
  },
  dateText: {
    fontSize: 20,
    fontWeight: 'bold',
  },
  buttonContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginTop: 'auto',
    paddingTop: 20,
  },
  button: {
    flex: 1,
    marginHorizontal: 5,
  },
  cancelButton: {
    borderWidth: 1,
  },
});