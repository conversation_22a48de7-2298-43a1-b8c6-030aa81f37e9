import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  SafeAreaView,
  Platform,
  StatusBar,
  TouchableOpacity,
  Image
} from 'react-native';
import { useTranslation } from 'react-i18next';
import { useRouter, useLocalSearchParams, Stack } from 'expo-router';
import { appStyleStore } from 'stores/app_style_store';
import { useApplyVolunteerOrgQualification } from '@/api/volunteer_services';
import { Button, Divider, Checkbox } from 'react-native-paper';
import { CustomDialog } from '@/common_modules/CustomDialog';
import MaterialCommunityIcons from '@expo/vector-icons/MaterialCommunityIcons';
import { createTheme } from 'theme/index';

const STATUSBAR_HEIGHT = Platform.OS === 'android' ? StatusBar.currentHeight || 0 : 0;

export default function VolunteerApplicationScreen() {
  const { t } = useTranslation();
  const theme = appStyleStore(state => state.theme || createTheme('red'));
  const router = useRouter();
  const params = useLocalSearchParams();
  const applyVolunteerOrgQualificationMutation = useApplyVolunteerOrgQualification();
  const [agreed, setAgreed] = useState(false);
  const [showError, setShowError] = useState(false);
  const [successDialogVisible, setSuccessDialogVisible] = useState(false);
  const [errorDialogVisible, setErrorDialogVisible] = useState(false);
  const [errorMessage, setErrorMessage] = useState('');
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [motivation, setMotivation] = useState('');

  // Get organization information from params
  const organizationId = typeof params.organizationId === 'string' ? params.organizationId : undefined;
  const organizationName = typeof params.organizationName === 'string' ? params.organizationName : t('programApplications.errors.organizationNotFound');
  const organizationImage = typeof params.organizationImage === 'string' ? params.organizationImage : undefined;
  const organizationDescription = typeof params.organizationDescription === 'string' ? params.organizationDescription : '';

  const styles = StyleSheet.create({
    safeArea: {
      flex: 1,
    },
    container: {
      flex: 1,
    },
    contentContainer: {
      paddingBottom: 24, // Adjusted to prevent overlap with the fixed footer
    },
    header: {
      padding: 16,
      paddingBottom: 8,
    },
    title: {
      fontSize: 24,
      fontWeight: '700',
      marginBottom: 8,
    },
    subtitle: {
      fontSize: 16,
      lineHeight: 22,
    },
    section: {
      paddingTop: 16,
      paddingBottom: 16,
      paddingHorizontal: 16,
    },
    sectionTitle: {
      fontSize: 16,
      fontWeight: '600',
      marginBottom: 12,
      letterSpacing: 0.3,
      lineHeight: 20,
    },
    divider: {
      height: StyleSheet.hairlineWidth,
    },
    termsBox: {
      backgroundColor: '#F5F5F5',
      borderRadius: 8,
      padding: 16,
      marginBottom: 16,
    },
    termsSectionTitle: {
      fontSize: 15,
      fontWeight: '600',
      color: theme.system.text,
      marginTop: 12,
      marginBottom: 4,
    },
    termsText: {
      fontSize: 14,
      lineHeight: 20,
      color: theme.system.secondaryText,
      marginBottom: 12,
    },
    checkboxContainer: {
      flexDirection: 'row',
      alignItems: 'center',
      marginBottom: 8,
    },
    checkboxLabel: {
      fontSize: 14,
      marginLeft: 8,
    },
    errorText: {
      color: theme.colors.error,
      fontSize: 14,
      marginTop: 4,
      marginLeft: 8,
    },
    organizationCard: {
      borderRadius: 8,
      flexDirection: 'row',
      alignItems: 'center',
    },
    organizationLogo: {
      width: 50,
      height: 50,
      borderRadius: 8,
      marginRight: 16,
    },
    logoPlaceholder: {
      width: 50,
      height: 50,
      borderRadius: 8,
      justifyContent: 'center',
      alignItems: 'center',
      marginRight: 16,
    },
    organizationName: {
      fontSize: 18,
      fontWeight: '600',
    },
    organizationDescription: {
      fontSize: 14,
      marginTop: 4,
      lineHeight: 20,
    },
    footer: {
      paddingHorizontal: 24,
      paddingVertical: 16,
      borderTopWidth: 1,
      // backgroundColor and borderTopColor will be set dynamically
    },
    submitButton: {
      flexDirection: 'row',
      alignItems: 'center',
      justifyContent: 'center',
      backgroundColor: theme.colors.primary,
      paddingVertical: 16,
      borderRadius: 12,
      gap: 8,
    },
    submitButtonDisabled: {
      backgroundColor: theme.colors.surfaceDisabled, // Fallback color for disabled state
    },
    submitButtonText: {
      fontSize: 17,
      fontWeight: '600',
      color: theme.colors.onPrimary,
    },
  });

  const handleSubmit = async () => {
    if (!agreed) {
      setShowError(true);
      return;
    }

    if (!organizationId) {
      setErrorMessage(t('programApplications.errors.organizationRequired'));
      setErrorDialogVisible(true);
      return;
    }

    setIsSubmitting(true);
    setShowError(false);
    setErrorMessage('');

    try {
      await applyVolunteerOrgQualificationMutation.mutateAsync({ orgId: organizationId });
      setSuccessDialogVisible(true);
    } catch (error: any) {
      console.error('Volunteer application submission error:', error);
      let specificMessage = t('programApplications.messages.submitError');
      
      // Handle 409 Conflict (Duplicate application)
      const apiStatus = error.status;
      const apiDetailedMessage = typeof error.message === 'string' ? error.message : null;
      
      if (apiStatus === 409) {
        // 409 - Conflict (Duplicate volunteer organization application)
        specificMessage = t('programApplications.messages.duplicateVolunteerOrgApplication');
      } else if (error.response && error.response.data && error.response.data.error) {
        const errorCode = error.response.data.error;
        const translationKey = `api.errors.${errorCode.toLowerCase()}`;
        specificMessage = t(translationKey, { defaultValue: specificMessage });
      } else if (error.message) {
        specificMessage = error.message;
      }
      setErrorMessage(specificMessage);
      setErrorDialogVisible(true);
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleSuccessConfirm = () => {
    setSuccessDialogVisible(false);
    router.back();
  };

  return (
    <>
      <Stack.Screen options={{ headerShown: true, headerTitle: t('programApplications.volunteer.title') }} />
      <SafeAreaView style={[styles.safeArea, { backgroundColor: theme.colors.background }]}>
        <ScrollView
          style={[styles.container, { backgroundColor: theme.colors.background }]}
          contentContainerStyle={styles.contentContainer}
        >
          <View style={styles.header}>
            <Text style={[styles.title, { color: theme.system.text }]}>
              {t('programApplications.volunteer.title')}
            </Text>
            <Text style={[styles.subtitle, { color: theme.system.secondaryText }]}>
              {t('programApplications.volunteer.description')}
            </Text>
          </View>

          {/* Organization Section */}
          <View style={styles.section}>
            <Text style={[styles.sectionTitle, { color: theme.system.secondaryText }]}>
              {t('programApplications.volunteer.organization')}
            </Text>

            <View>
              <View style={styles.organizationCard}>
                {organizationImage ? (
                  <Image
                    source={{ uri: organizationImage }}
                    style={styles.organizationLogo}
                    resizeMode="contain"
                  />
                ) : (
                  <View style={[styles.logoPlaceholder, { backgroundColor: theme.colors.primaryContainer }]}>
                    <Text style={{ color: theme.colors.primary, fontSize: 20, fontWeight: 'bold' }}>
                      {organizationName.substring(0, 1)}
                    </Text>
                  </View>
                )}
                <View style={{ flex: 1 }}>
                  <Text style={[styles.organizationName, { color: theme.system.text }]}>
                    {organizationName}
                  </Text>
                  {organizationDescription && (
                    <Text style={[styles.organizationDescription, { color: theme.system.secondaryText }]}>
                      {organizationDescription}
                    </Text>
                  )}
                </View>
              </View>
            </View>
          </View>

          <Divider style={[styles.divider, { backgroundColor: theme.system.border }]} />

          {/* Terms and Conditions Section */}
          <View style={styles.section}>
            <Text style={[styles.sectionTitle, { color: theme.system.secondaryText }]}>
              {t('programApplications.modal.termsAndConditions')}
            </Text>

            <View style={styles.termsBox}>
              <Text style={styles.termsSectionTitle}>{t('programApplications.modal.terms.responsibilities')}</Text>
              <Text style={styles.termsText}>{t('programApplications.modal.terms.volunteerResponsibilities')}</Text>

              <Text style={styles.termsSectionTitle}>{t('programApplications.modal.terms.requirements')}</Text>
              <Text style={styles.termsText}>{t('programApplications.modal.terms.volunteerRequirements')}</Text>

              <Text style={styles.termsSectionTitle}>{t('programApplications.modal.terms.benefits')}</Text>
              <Text style={styles.termsText}>{t('programApplications.modal.terms.volunteerBenefits')}</Text>
            </View>

            <View style={styles.checkboxContainer}>
              <Checkbox.Android
                status={agreed ? 'checked' : 'unchecked'}
                onPress={() => {
                  setAgreed(!agreed);
                  if (showError) setShowError(false);
                }}
                color={theme.colors.primary}
              />
              <Text
                style={[styles.checkboxLabel, { color: theme.system.text }]}
                onPress={() => {
                  setAgreed(!agreed);
                  if (showError) setShowError(false);
                }}
              >
                {t('programApplications.modal.agreeToTerms')}
              </Text>
            </View>

            {showError && (
              <Text style={styles.errorText}>
                {t('programApplications.modal.messages.agreementRequired')}
              </Text>
            )}
          </View>
        </ScrollView>
        {/* Submit Button Area */}
        <View style={[styles.footer, { backgroundColor: theme.system.background, borderTopColor: theme.system.border }]}>
          <TouchableOpacity
            style={[styles.submitButton, (!organizationId || isSubmitting) && styles.submitButtonDisabled]}
            onPress={handleSubmit}
            disabled={!organizationId || isSubmitting}
          >
            {isSubmitting ? (
              <Text style={styles.submitButtonText}>{t('common.loading')}</Text>
            ) : (
              <>
                <Text style={styles.submitButtonText}>{t('programApplications.modal.submit')}</Text>
                <MaterialCommunityIcons name="arrow-right" size={20} color={theme.colors.onPrimary} />
              </>
            )}
          </TouchableOpacity>
        </View>


        <CustomDialog
          visible={successDialogVisible}
          title={t('common.success')}
          message={t('programApplications.messages.volunteerSubmitSuccess')}
          confirmText={t('common.ok')}
          onConfirm={handleSuccessConfirm}
          type="success"
        />

        <CustomDialog
          visible={errorDialogVisible}
          title={t('common.error')}
          message={errorMessage}
          confirmText={t('common.ok')}
          onConfirm={() => setErrorDialogVisible(false)}
          type="error"
        />
      </SafeAreaView>
    </>
  );
}