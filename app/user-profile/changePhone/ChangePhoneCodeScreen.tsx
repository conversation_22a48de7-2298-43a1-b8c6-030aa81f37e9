import React, { useEffect, useState } from 'react';
import { View, Text } from 'react-native';
import { Button } from 'react-native-paper';
import { useLocalSearchParams, useRouter } from 'expo-router';
import { useTranslation } from 'react-i18next';
import BaseCodeVerificationScreen from '@/app/login/components/BaseCodeVerificationScreen';
import { CustomDialog } from '@/common_modules/CustomDialog';
import {
    useChangePhoneOtpInitiate,
    useChangePhoneOtpVerify
} from '@/api/user_services';
import {
    ChangePhoneOtpInitiateRequest as InitiatePhoneChangeRequest,
    ChangePhoneOtpVerifyRequest as VerifyPhoneChangeRequest
} from '@/api/api_config';

export default function ChangePhoneCodeScreen() {
  const { t } = useTranslation();
  const router = useRouter();
  const params = useLocalSearchParams<{ phoneNumber: string, flowId?: string, method?: string, state?: string }>();
  const { mutateAsync: verifyPhoneChange } = useChangePhoneOtpVerify();
  const { mutateAsync: initiatePhoneChange } = useChangePhoneOtpInitiate();

  const newPhoneNumber = params.phoneNumber || "";
  const verificationMethod = (params.method || 'whatsapp') as 'sms' | 'whatsapp';
  const flowState = params.state;

  const [successDialogVisible, setSuccessDialogVisible] = useState(false);
  const [currentFlowState, setCurrentFlowState] = useState<string | undefined>(flowState);

  useEffect(() => {
    setCurrentFlowState(flowState);
    console.log('Profile Change - ChangePhoneCodeScreen loaded');
    console.log('Verifying NEW phone number:', newPhoneNumber);
    console.log('Verification method:', verificationMethod);
    if (!currentFlowState) {
      console.error('CRITICAL: Flow state is missing. Cannot verify new phone.');
    }
  }, [newPhoneNumber, verificationMethod, flowState]);

  const handleVerifyNewPhoneCode = async (code: string) => {
    if (!currentFlowState) {
      console.error('Verification state (currentFlowState) is missing. Cannot proceed.');
      throw new Error(t('error.phoneVerification.INVALID_STATE'));
    }
    console.log('Verifying OTP for NEW phone:', code, 'for number:', newPhoneNumber, 'with state:', currentFlowState);

    const verifyParams: VerifyPhoneChangeRequest = {
      otp: code,
      new_phone_number: newPhoneNumber,
      state: currentFlowState,
    };

    try {
      const response = await verifyPhoneChange(verifyParams);
      console.log('New phone verification successful:', response);
      setSuccessDialogVisible(true);
      return response;
    } catch (error: any) {
      console.error('New phone verification process error:', error);
      let detailedErrorMessage = t('error.phoneVerification.SYSTEM_ERROR');
      if (error && error.response && error.response.data && error.response.data.error) {
        const backendErrorCode = error.response.data.error;
        detailedErrorMessage = t(`error.phoneVerification.${backendErrorCode}`, { defaultValue: detailedErrorMessage });
      } else if (error && error.message) {
        detailedErrorMessage = error.message;
      }
      throw new Error(detailedErrorMessage);
    }
  };

  const handleResendNewPhoneCode = async () => {
    console.log('Resending code for NEW phone:', newPhoneNumber, 'method:', verificationMethod);
    
    // const pkceResult = await generatePKCECredentials(); // Not needed for /users/me/phone/initiate-change
    // if (!pkceResult || !pkceResult.state) {
    //   console.error('Failed to generate PKCE credentials for resend.');
    //   throw new Error(t('error.phoneVerification.STATE_GENERATION_FAILURE', {defaultValue: 'Could not prepare for resend. Please try again.'}));
    // }
    // const newResendState = pkceResult.state;

    // Generate a simple random state, as used in ChangePhoneScreen.tsx
    const newResendState = Math.random().toString(36).substring(2, 15) + Math.random().toString(36).substring(2, 15);

    const initiateParams: InitiatePhoneChangeRequest = {
      new_phone_number: newPhoneNumber,
      phone_otp_channel: 'whatsapp',
      client_id: 'phone-change-flow',
      state: newResendState,
    };

    try {
      const response = await initiatePhoneChange(initiateParams);
      setCurrentFlowState(newResendState);
      console.log('Code resent successfully for new phone. New state for verify:', newResendState);
    } catch (error: any) {
      console.error('Resend code error for new phone:', error);
      let detailedErrorMessage = t('error.phoneVerification.SYSTEM_ERROR');
      if (error && error.response && error.response.data && error.response.data.error) {
        const backendErrorCode = error.response.data.error;
        detailedErrorMessage = t(`error.phoneVerification.${backendErrorCode}`, { defaultValue: detailedErrorMessage });
      } else if (error && error.message) {
        detailedErrorMessage = error.message; 
      }
      throw new Error(detailedErrorMessage);
    }
  };
  
  const handleSuccessNavigation = () => {
    // BaseScreen calls this, then we show dialog.
    // Dialog confirm will do the navigation.
    // This function itself doesn't need to do anything for navigation now.
  };

  const onDialogConfirm = () => {
    router.dismissTo('/tabs/profile');
  };

  if (!currentFlowState) {
    return (
      <View style={{flex: 1, justifyContent: 'center', alignItems: 'center', padding: 20}}>
        <Text style={{textAlign: 'center', fontSize: 16, color: 'red'}}>
          {t('error.phoneVerification.INVALID_STATE_MISSING')} 
        </Text>
        <Button onPress={() => router.back()}>{t('common.goBack')}</Button>
      </View>
    )
  }

  return (
    <>
      <BaseCodeVerificationScreen
        phoneNumber={newPhoneNumber}
        verificationMethod={verificationMethod}
        onVerify={handleVerifyNewPhoneCode}
        onResendCode={handleResendNewPhoneCode}
        onSuccessNavigation={handleSuccessNavigation}
        screenStackTitleKey="profile.changePhone.verifyNewPhoneCode"
        headerSubtitleKey="profile.changePhone.verifyNewPhoneCodeDesc"
        showDisplayNameInput={false}
        isNewUserFlow={false}
      />
      <CustomDialog
        visible={successDialogVisible}
        onConfirm={onDialogConfirm}
        title={t('profile.changePhone.success.title')}
        message={t('profile.changePhone.success.message')}
        confirmText={t('common.ok')}
        type="success"
      />
    </>
  );
}
