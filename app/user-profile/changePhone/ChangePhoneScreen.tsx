import React, { useState, useRef } from 'react';
import { View, Text, StyleSheet, TextInput, KeyboardAvoidingView, Platform, Alert, TouchableWithoutFeedback, Keyboard } from 'react-native';
import { Button } from 'react-native-paper';
import MaterialCommunityIcons from '@expo/vector-icons/MaterialCommunityIcons';
import { useRouter, Stack } from 'expo-router';
import { useTranslation } from 'react-i18next';
import { whatsAppTheme, createTheme } from 'theme/index';
import { appStyleStore } from 'stores/app_style_store';
import { useChangePhoneOtpInitiate } from '@/api/user_services';
import { PhoneOtpInitiateResponse } from '@/api/api_config';

export default function ChangePhoneScreen() {
  const router = useRouter();
  const { t } = useTranslation();
  const storedTheme = appStyleStore(state => state.theme);
  const theme = storedTheme || createTheme('red'); 

  const { mutateAsync: initiatePhoneChange, error: initiatePhoneChangeError } = useChangePhoneOtpInitiate();

  const [phoneNumber, setPhoneNumber] = useState('');
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  
  const phoneInputRef = useRef<TextInput>(null);

  const formatPhoneNumber = (input: string) => {
    const numbers = input.replace(/\D/g, '');
    if (numbers.length <= 4) {
      return numbers;
    } else {
      return `${numbers.slice(0, 4)} ${numbers.slice(4, 8)}`;
    }
  };

  const handlePhoneNumberChange = (text: string) => {
    setError(null);
    const formatted = formatPhoneNumber(text);
    if (formatted.length <= 9) {
      setPhoneNumber(formatted);
    }
  };

  const handleSendCodeViaWhatsApp = async () => {
    if (!isPhoneNumberValid) return;
    
    setLoading(true);
    setError(null);

    const cleanedPhoneNumber = '+852' + phoneNumber.replace(/\s/g, '');
    const clientState = Math.random().toString(36).substring(2, 15) + Math.random().toString(36).substring(2, 15);

    console.log('[ChangePhoneScreen] Initiating phone change with:', {
      client_id: 'phone-change-flow',
      new_phone_number: cleanedPhoneNumber,
      phone_otp_channel: 'whatsapp',
      state: clientState
    });

    try {
      const outcome: PhoneOtpInitiateResponse = await initiatePhoneChange({
        client_id: 'phone-change-flow',
        new_phone_number: cleanedPhoneNumber,
        phone_otp_channel: 'whatsapp',
        state: clientState
      });

      setLoading(false);

      if (outcome && outcome.state) {
        console.log('[ChangePhoneScreen] Phone change initiation successful:', outcome);

        Keyboard.dismiss();
        router.push({
          pathname: '/user-profile/changePhone/ChangePhoneCodeScreen',
          params: {
            phoneNumber: cleanedPhoneNumber,
            method: 'whatsapp',
            state: outcome.state,
          }
        });
      } else {
        console.error('[ChangePhoneScreen] Successful initiation but API response missing state:', outcome);
        setError(t('error.phoneVerification.SYSTEM_ERROR', 'System error. Missing state from server.'));
      }
    } catch (apiError: any) {
      setLoading(false);
      console.warn('[ChangePhoneScreen] Phone change initiation failed. API Error:', apiError);
      
      let displayError = t('error.phoneVerification.SYSTEM_ERROR', 'An unexpected error occurred.');
      if (apiError.response && apiError.response.data && apiError.response.data.error) {
        const backendErrorCode = apiError.response.data.error;
        if (backendErrorCode === 'CONFLICT_PHONE_EXISTS') {
          displayError = t('error.phoneVerification.CONFLICT_PHONE_EXISTS', 'This phone number is already in use.');
        } else {
          displayError = t(`error.api.${backendErrorCode}`, { defaultValue: displayError });
        }
      } else if (apiError.message) {
        displayError = apiError.message; 
      }
      setError(displayError);
    }
  };

  const isPhoneNumberValid = phoneNumber.replace(/\s/g, '').length === 8;

  return (
    <>
      <Stack.Screen options={{ title: t('profile.changePhone.title') }} />
      <KeyboardAvoidingView 
        behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
        style={[styles.container, { backgroundColor: theme.colors.background }]}
      >
        <TouchableWithoutFeedback onPress={Keyboard.dismiss} accessible={false}>
          <View style={styles.contentContainer}>
            <View style={styles.header}>
              <MaterialCommunityIcons name="cellphone-message" size={48} color={theme.colors.primary} />
              <Text style={[styles.title, { color: theme.colors.primary }]}>{t('profile.changePhone.smsVerification')}</Text>
              <Text style={[styles.subtitle, { color: theme.system.secondaryText }]}>{t('profile.changePhone.smsVerificationDesc')}</Text>
            </View>

            <View style={styles.inputContainer}>
              <View style={[styles.phoneInputContainer, { borderColor: theme.colors.primary }]}>
                <Text style={[styles.prefix, { color: theme.system.text }]}>{t('auth.phonePrefix')}</Text>
                <TextInput
                  ref={phoneInputRef}
                  style={[styles.phoneInput, { color: theme.system.text }]}
                  value={phoneNumber}
                  onChangeText={handlePhoneNumberChange}
                  placeholder={t('auth.phonePlaceholder')}
                  keyboardType="number-pad"
                  maxLength={9}
                  placeholderTextColor={'#888888'}
                />
              </View>

              {error && <Text style={[styles.errorText, { color: theme.system.error }]}>{error}</Text>}

              <Button
                mode="contained"
                onPress={handleSendCodeViaWhatsApp}
                disabled={!isPhoneNumberValid || loading}
                style={[
                  styles.button,
                  { backgroundColor: whatsAppTheme.colors.primary },
                  (!isPhoneNumberValid || loading) && { backgroundColor: whatsAppTheme.colors.primaryDisabled }
                ]}
                contentStyle={styles.buttonContent}
                labelStyle={[
                  styles.buttonLabel,
                  { color: '#FFFFFF' },
                ]}
                icon={({ size }) => (
                  <MaterialCommunityIcons name="whatsapp" size={24} color="#FFFFFF" />
                )}
                loading={loading}
              >
                {t('profile.changePhone.useWhatsApp')}
              </Button>
            </View>
          </View>
        </TouchableWithoutFeedback>
      </KeyboardAvoidingView>
    </>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    padding: 24,
  },
  contentContainer: {
    flex: 1,
    width: '100%',
  },
  header: {
    alignItems: 'center',
    marginTop: 20,
    marginBottom: 40,
  },
  title: {
    fontSize: 24,
    fontWeight: '600',
    marginTop: 16,
    marginBottom: 8,
  },
  subtitle: {
    fontSize: 16,
    textAlign: 'center',
  },
  inputContainer: {
    alignItems: 'center',
  },
  phoneInputContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    borderWidth: 2,
    borderRadius: 12,
    paddingHorizontal: 16,
    marginBottom: 16,
    height: 48,
    width: '100%',
  },
  prefix: {
    fontSize: 16,
    fontWeight: '600',
    marginRight: 8,
  },
  phoneInput: {
    flex: 1,
    fontSize: 16,
  },
  errorText: {
    fontSize: 14,
    marginBottom: 16,
    textAlign: 'center',
  },
  button: {
    borderRadius: 12,
    width: '100%',
    height: 48,
    marginBottom: 24,
  },
  buttonContent: {
    height: 48,
  },
  buttonLabel: {
    fontSize: 16,
    fontWeight: '600',
  },
});
