import React, { useState } from 'react';
import { StatusBar, View, Text, TouchableOpacity, StyleSheet, Platform, Alert, ActivityIndicator } from 'react-native';
import { MaterialCommunityIcons } from '@expo/vector-icons';
import { useTranslation } from 'react-i18next';
import ImageViewing from 'react-native-image-viewing';
import * as MediaLibrary from 'expo-media-library';
import * as FileSystem from 'expo-file-system';
import { MediaItemPayload } from '@/api/api_config';
import { getValidImageUrl } from 'utils/imageUtils';

interface ImageViewerProps {
    visible: boolean;
    images: MediaItemPayload[];
    initialIndex: number;
    onClose: () => void;
    theme: any;
}

// Check if file is an image based on extension
const isImageFile = (fileName: string | undefined): boolean => {
    if (!fileName) return false;
    const extension = fileName.split('.').pop()?.toLowerCase() || '';
    const imageTypes = ['jpg', 'jpeg', 'png', 'gif', 'bmp', 'webp', 'heic', 'heif'];
    return imageTypes.includes(extension);
};

export const ImageViewer: React.FC<ImageViewerProps> = ({
    visible,
    images,
    initialIndex,
    onClose,
    theme,
}) => {
    const { t } = useTranslation();
    const [isDownloading, setIsDownloading] = useState(false);
    
    // Filter and format images for the viewer
    const imageItems = images
        .filter(item => isImageFile(item.file_name))
        .map(item => ({
            uri: getValidImageUrl(item.file_path) || '',
        }));

    if (imageItems.length === 0) {
        return null;
    }

    const downloadImage = async (imageIndex: number) => {
        try {
            setIsDownloading(true);
            
            // Request permission
            const { status } = await MediaLibrary.requestPermissionsAsync();
            if (status !== 'granted') {
                Alert.alert(
                    t('events.detail.imageDownload.permissionRequired'), 
                    t('events.detail.imageDownload.permissionMessage')
                );
                return;
            }

            const imageUri = imageItems[imageIndex]?.uri;
            if (!imageUri) {
                Alert.alert(
                    t('events.detail.imageDownload.error'), 
                    t('events.detail.imageDownload.imageNotFound')
                );
                return;
            }

            // Download the image to local cache
            const fileUri = `${FileSystem.cacheDirectory}image_${Date.now()}.jpg`;
            const downloadResult = await FileSystem.downloadAsync(imageUri, fileUri);
            
            if (downloadResult.status === 200) {
                // Save to media library
                const asset = await MediaLibrary.createAssetAsync(downloadResult.uri);
                await MediaLibrary.createAlbumAsync('Downloads', asset, false);
                
                Alert.alert(
                    t('events.detail.imageDownload.success'), 
                    t('events.detail.imageDownload.successMessage')
                );
            } else {
                throw new Error('Download failed');
            }
        } catch (error) {
            console.error('Download error:', error);
            Alert.alert(
                t('events.detail.imageDownload.error'), 
                t('events.detail.imageDownload.errorMessage')
            );
        } finally {
            setIsDownloading(false);
        }
    };

    const HeaderComponent = ({ imageIndex: currentIndex }: { imageIndex: number }) => (
        <View style={[
            styles.header,
            { paddingTop: Platform.OS === 'ios' ? 44 : 0 }
        ]}>
            <TouchableOpacity
                style={styles.headerButton}
                onPress={onClose}
            >
                <MaterialCommunityIcons name="close" size={24} color="#FFFFFF" />
            </TouchableOpacity>
            <Text style={styles.headerTitle}>
                {currentIndex + 1} / {imageItems.length}
            </Text>
            <TouchableOpacity
                style={styles.headerButton}
                onPress={() => downloadImage(currentIndex)}
                disabled={isDownloading}
            >
                {isDownloading ? (
                    <ActivityIndicator 
                        size="small" 
                        color="#FFFFFF" 
                    />
                ) : (
                    <MaterialCommunityIcons 
                        name="download" 
                        size={24} 
                        color="#FFFFFF" 
                    />
                )}
            </TouchableOpacity>
        </View>
    );

    const FooterComponent = ({ imageIndex: currentIndex }: { imageIndex: number }) => (
        <View style={styles.footer}>
            <View style={styles.paginationContainer}>
                {imageItems.map((_, index) => (
                    <View
                        key={`dot-${index}`}
                        style={[
                            styles.paginationDot,
                            index === currentIndex
                                ? styles.paginationDotActive
                                : styles.paginationDotInactive
                        ]}
                    />
                ))}
            </View>
        </View>
    );

    return (
        <>
            <StatusBar backgroundColor="#000000" barStyle="light-content" />
            <ImageViewing
                images={imageItems}
                imageIndex={initialIndex}
                visible={visible}
                onRequestClose={onClose}
                doubleTapToZoomEnabled={true}
                presentationStyle="overFullScreen"
                animationType="fade"
                backgroundColor="#000000"
                HeaderComponent={HeaderComponent}
                FooterComponent={FooterComponent}
            />
        </>
    );
};

const styles = StyleSheet.create({
    header: {
        position: 'absolute',
        top: 0,
        left: 0,
        right: 0,
        height: 60 + (Platform.OS === 'ios' ? 44 : 0),
        flexDirection: 'row',
        justifyContent: 'space-between',
        alignItems: 'center',
        paddingHorizontal: 20,
        zIndex: 10,
        backgroundColor: 'rgba(0, 0, 0, 0.3)',
    },
    headerButton: {
        width: 40,
        height: 40,
        borderRadius: 20,
        backgroundColor: 'rgba(0, 0, 0, 0.5)',
        justifyContent: 'center',
        alignItems: 'center',
    },
    headerTitle: {
        color: '#FFFFFF',
        fontSize: 16,
        fontWeight: '600',
    },
    footer: {
        position: 'absolute',
        bottom: 0,
        left: 0,
        right: 0,
        paddingBottom: Platform.OS === 'ios' ? 34 : 20,
        paddingTop: 20,
        backgroundColor: 'rgba(0, 0, 0, 0.3)',
        alignItems: 'center',
    },
    paginationContainer: {
        flexDirection: 'row',
        justifyContent: 'center',
        alignItems: 'center',
        marginBottom: 10,
    },
    paginationDot: {
        width: 8,
        height: 8,
        borderRadius: 4,
        marginHorizontal: 4,
    },
    paginationDotActive: {
        backgroundColor: '#FFFFFF',
    },
    paginationDotInactive: {
        backgroundColor: 'rgba(255, 255, 255, 0.5)',
    },
    instructionText: {
        color: 'rgba(255, 255, 255, 0.7)',
        fontSize: 12,
        textAlign: 'center',
        paddingHorizontal: 20,
    },
});

export default ImageViewer;