import React, { useState } from 'react';
import {
    View,
    Text,
    StyleSheet,
    TouchableOpacity,
    ActivityIndicator,
} from 'react-native';
import { MaterialCommunityIcons } from '@expo/vector-icons';
import { useTranslation } from 'react-i18next';
import { parseISO, isPast } from 'date-fns';
import type { EventListPayloadDetails } from '@/api/api_config';
import { authenticationStore } from 'stores/authentication_store';
import { useRegisterForEvent, useCancelEventRegistration, useFetchRegisteredEventsList } from '@/api/user_events_services';
import { useFetchUserVolunteerQualifications, useFetchUserVerifications } from '@/api/user_services';
import { useFetchUserEventVolunteerApplicationsList, useApplyEventVolunteer, useWithdrawEventVolunteer } from '@/api/volunteer_services';
import { CustomDialog } from '@/common_modules/CustomDialog';
import { useRouter } from 'expo-router';

interface EventActionButtonsProps {
    event: EventListPayloadDetails;
    theme: any;
    onAuthRequired: () => void;
    isLoading?: boolean;
    onRegistrationChange?: () => void; // Callback to refresh event data
}

export const EventActionButtons: React.FC<EventActionButtonsProps> = ({
    event,
    theme,
    onAuthRequired,
    isLoading = false,
    onRegistrationChange,
}) => {
    const { t } = useTranslation();
    const router = useRouter();
    const isAuthenticated = authenticationStore(state => state.isAuthenticated);
    
    // Dialog state management
    const [dialogVisible, setDialogVisible] = useState(false);
    const [dialogType, setDialogType] = useState<'success' | 'error'>('success');
    const [dialogTitle, setDialogTitle] = useState('');
    const [dialogMessage, setDialogMessage] = useState('');
    
    // Confirmation dialog state
    const [confirmDialogVisible, setConfirmDialogVisible] = useState(false);
    const [confirmDialogTitle, setConfirmDialogTitle] = useState('');
    const [confirmDialogMessage, setConfirmDialogMessage] = useState('');
    const [pendingAction, setPendingAction] = useState<(() => Promise<void>) | null>(null);
    
    // Verification dialog state
    const [verificationDialogVisible, setVerificationDialogVisible] = useState(false);
    const [missingVerificationsList, setMissingVerificationsList] = useState<string[]>([]);
    
    // API hooks
    const registerMutation = useRegisterForEvent();
    const cancelMutation = useCancelEventRegistration();
    const volunteerApplyMutation = useApplyEventVolunteer();
    const volunteerWithdrawMutation = useWithdrawEventVolunteer();
    
    // Fetch user's registered events to check if already registered
    const { data: registeredEvents, isLoading: isLoadingRegisteredEvents } = useFetchRegisteredEventsList({
        event_id: event.id
    });
    
    // Fetch user's volunteer qualifications to check if they can volunteer for this organization
    const { data: volunteerQualifications, isLoading: isLoadingVolunteerQualifications } = useFetchUserVolunteerQualifications(
        {},
        isAuthenticated
    );
    
    // Fetch user's volunteer applications for this specific event
    const { data: volunteerApplications, isLoading: isLoadingVolunteerApplications } = useFetchUserEventVolunteerApplicationsList(
        isAuthenticated ? { 
            event_id: event.id, 
            limit: 1, 
            offset: 0 
        } : undefined
    );
    
    // Fetch user's verification status
    const { data: userVerifications, isLoading: isLoadingVerifications } = useFetchUserVerifications();
    
    // Check if user meets verification requirements
    const checkVerificationRequirements = (): { isCompliant: boolean; missingVerifications: string[] } => {
        if (!isAuthenticated || !userVerifications || !event.verification_type_keys) {
            return { isCompliant: true, missingVerifications: [] };
        }
        
        const approvedVerifications = userVerifications
            .filter(verification => verification.status === 'approved')
            .map(verification => verification.verification_type);
        
        const missingVerifications = event.verification_type_keys.filter(
            requiredType => !approvedVerifications.includes(requiredType)
        );
        
        return {
            isCompliant: missingVerifications.length === 0,
            missingVerifications
        };
    };

    // Check if event has ended
    const eventEnded = isPast(parseISO(event.end_time || event.start_time));

    // If event has ended, don't show any buttons
    if (eventEnded) {
        return null;
    }

    // Check if user is already registered for this event
    const userRegistration = registeredEvents?.find(registration => registration.event_id === event.id);
    
    // Only consider user as registered if they have an active registration (not cancelled)
    const activeRegistrationStatuses = ['registered', 'approved', 'pending_approval', 'waitlisted', 'pending_payment', 'confirmed'];
    const isRegistered = isAuthenticated && !!userRegistration && activeRegistrationStatuses.includes(userRegistration.status);
    
    // Check event capacity status
    const isEventFull = (event.registered_count || 0) >= (event.participant_limit || 0);
    const isWaitlistFull = (event.waitlisted_count || 0) >= (event.waitlist_limit || 0);
    const hasWaitlistSpace = !isWaitlistFull && (event.waitlist_limit || 0) > 0;
    
    // Check if user has volunteer qualification for this event's organization
    const hasVolunteerPermission = isAuthenticated && volunteerQualifications?.some(
        qualification => qualification.organization_id === event.organization_id && qualification.status === 'approved'
    );
    
    // Check if user already has volunteer application for this event
    const userVolunteerApplication = volunteerApplications?.find(application => application.event_id === event.id);
    const activeVolunteerStatuses = ['pending', 'approved', 'confirmed', 'applied'];
    const isVolunteerApplied = isAuthenticated && !!userVolunteerApplication && activeVolunteerStatuses.includes(userVolunteerApplication.status);
    
    // Check volunteer application statuses
    const isVolunteerPending = isAuthenticated && !!userVolunteerApplication && 
        userVolunteerApplication.status === 'pending';
    const canCancelVolunteerApplication = isAuthenticated && !!userVolunteerApplication && 
        ['approved', 'applied'].includes(userVolunteerApplication.status);

    // Show success/error dialog
    const showDialog = (type: 'success' | 'error', titleKey: string, messageKey: string) => {
        setDialogType(type);
        setDialogTitle(t(titleKey));
        setDialogMessage(t(messageKey));
        setDialogVisible(true);
    };
    
    // Show confirmation dialog
    const showConfirmDialog = (titleKey: string, messageKey: string, action: () => Promise<void>) => {
        setConfirmDialogTitle(t(titleKey));
        setConfirmDialogMessage(t(messageKey));
        setPendingAction(() => action);
        setConfirmDialogVisible(true);
    };
    
    // Handle confirmation dialog response
    const handleConfirmAction = async () => {
        setConfirmDialogVisible(false);
        if (pendingAction) {
            await pendingAction();
            setPendingAction(null);
        }
    };
    
    const handleCancelAction = () => {
        setConfirmDialogVisible(false);
        setPendingAction(null);
    };

    const handleParticipantButtonPress = async () => {
        if (!isAuthenticated) {
            onAuthRequired();
            return;
        }
        
        if (isRegistered) {
            // Show confirmation dialog for cancelling registration
            showConfirmDialog(
                'events.detail.confirmCancelRegistration',
                'events.detail.confirmCancelRegistrationMessage',
                async () => {
                    if (userRegistration?.id) {
                        try {
                            await cancelMutation.mutateAsync({
                                registrationId: userRegistration.id,
                                eventId: event.id
                            });
                            showDialog('success', 'events.detail.cancelSuccess', 'events.detail.cancelSuccessMessage');
                            onRegistrationChange?.();
                        } catch (error) {
                            showDialog('error', 'events.detail.cancelError', 'events.detail.cancelErrorMessage');
                        }
                    }
                }
            );
        } else {
            // Check if event is full and no waitlist space first
            if (isEventFull && !hasWaitlistSpace) {
                // Event is completely full, do nothing (button is disabled)
                return;
            }
            
            // Check verification requirements before registering
            const { isCompliant, missingVerifications } = checkVerificationRequirements();
            
            if (!isCompliant && missingVerifications.length > 0) {
                // Show verification requirements dialog with bullet points
                setMissingVerificationsList(missingVerifications);
                setVerificationDialogVisible(true);
                return;
            }
            
            // Determine registration type and show appropriate confirmation
            const isJoiningWaitlist = isEventFull && hasWaitlistSpace;
            const confirmTitleKey = isJoiningWaitlist ? 'events.detail.confirmJoinWaitlist' : 'events.detail.confirmRegister';
            const confirmMessageKey = isJoiningWaitlist ? 'events.detail.confirmJoinWaitlistMessage' : 'events.detail.confirmRegisterMessage';
            const successTitleKey = isJoiningWaitlist ? 'events.detail.waitlistSuccess' : 'events.detail.registerSuccess';
            const successMessageKey = isJoiningWaitlist ? 'events.detail.waitlistSuccessMessage' : 'events.detail.registerSuccessMessage';
            const errorTitleKey = isJoiningWaitlist ? 'events.detail.waitlistError' : 'events.detail.registerError';
            const errorMessageKey = isJoiningWaitlist ? 'events.detail.waitlistErrorMessage' : 'events.detail.registerErrorMessage';
            
            // Show confirmation dialog for registering
            showConfirmDialog(
                confirmTitleKey,
                confirmMessageKey,
                async () => {
                    try {
                        await registerMutation.mutateAsync({
                            event_id: event.id
                        });
                        showDialog('success', successTitleKey, successMessageKey);
                        onRegistrationChange?.();
                    } catch (error) {
                        showDialog('error', errorTitleKey, errorMessageKey);
                    }
                }
            );
        }
    };
    
    const handleVolunteerButtonPress = async () => {
        if (!isAuthenticated) {
            onAuthRequired();
            return;
        }
        
        // If volunteer application is pending, do nothing (button is disabled)
        if (isVolunteerPending) {
            return;
        }
        
        if (canCancelVolunteerApplication) {
            // Show confirmation dialog for cancelling volunteer application
            showConfirmDialog(
                'events.detail.confirmVolunteerCancel',
                'events.detail.confirmVolunteerCancelMessage',
                async () => {
                    if (userVolunteerApplication?.id) {
                        try {
                            await volunteerWithdrawMutation.mutateAsync({
                                appId: userVolunteerApplication.id,
                                eventId: event.id
                            });
                            showDialog('success', 'events.detail.volunteerCancelSuccess', 'events.detail.volunteerCancelSuccessMessage');
                            onRegistrationChange?.();
                        } catch (error) {
                            showDialog('error', 'events.detail.volunteerCancelError', 'events.detail.volunteerCancelErrorMessage');
                        }
                    }
                }
            );
        } else {
            // Show confirmation dialog for applying as volunteer
            showConfirmDialog(
                'events.detail.confirmVolunteerApply',
                'events.detail.confirmVolunteerApplyMessage',
                async () => {
                    try {
                        await volunteerApplyMutation.mutateAsync({
                            eventId: event.id
                            // motivation is optional, we can add it later if needed
                        });
                        showDialog('success', 'events.detail.volunteerApplySuccess', 'events.detail.volunteerApplySuccessMessage');
                        onRegistrationChange?.();
                    } catch (error) {
                        showDialog('error', 'events.detail.volunteerApplyError', 'events.detail.volunteerApplyErrorMessage');
                    }
                }
            );
        }
    };

    const getParticipantButtonText = () => {
        if (!isAuthenticated) {
            return t('auth.modal.loginButton');
        }
        if (isRegistered) {
            return t('events.detail.cancelRegistration');
        }
        // Check capacity status for unregistered users
        if (isEventFull && !hasWaitlistSpace) {
            return t('events.detail.eventFull');
        }
        if (isEventFull && hasWaitlistSpace) {
            return t('events.detail.joinWaitlist');
        }
        return t('events.detail.register');
    };
    
    const getVolunteerButtonText = () => {
        if (!isAuthenticated) {
            return t('auth.modal.loginButton');
        }
        if (isVolunteerPending) {
            return t('events.detail.volunteerPendingApproval');
        }
        if (canCancelVolunteerApplication) {
            return t('events.detail.cancelVolunteer');
        }
        return t('events.detail.becomeVolunteer');
    };

    const isButtonLoading = isLoading || registerMutation.isPending || cancelMutation.isPending || 
                           volunteerApplyMutation.isPending || volunteerWithdrawMutation.isPending ||
                           isLoadingRegisteredEvents || isLoadingVolunteerQualifications || isLoadingVolunteerApplications;

    const styles = StyleSheet.create({
        container: {
            paddingHorizontal: 20,
            paddingTop: 16,
            paddingBottom: 32,
            backgroundColor: theme.system.background,
            borderTopWidth: 1,
            borderTopColor: theme.system.border,
        },
        button: {
            backgroundColor: theme.colors.primary,
            borderRadius: 12,
            paddingVertical: 16,
            paddingHorizontal: 24,
            flexDirection: 'row',
            alignItems: 'center',
            justifyContent: 'center',
            gap: 8,
            borderWidth: 1,
            borderColor: theme.colors.primary,
        },
        buttonOutline: {
            backgroundColor: 'transparent',
            borderColor: theme.colors.primary,
        },
        buttonDisabled: {
            backgroundColor: theme.system.border,
            borderColor: theme.system.border,
        },
        buttonWaitlist: {
            backgroundColor: '#FF9800', // Orange/yellow color for waitlist
            borderColor: '#FF9800',
        },
        buttonText: {
            color: theme.colors.onPrimary,
            fontSize: 16,
            fontWeight: '600',
        },
        buttonTextOutline: {
            color: theme.colors.primary,
        },
        buttonTextDisabled: {
            color: theme.system.secondaryText,
        },
        loadingContainer: {
            flexDirection: 'row',
            alignItems: 'center',
            gap: 8,
        },
        buttonContainer: {
            flexDirection: 'row',
            gap: 12,
        },
        volunteerButton: {
            backgroundColor: '#E91E63', // Pink color for volunteer
            borderColor: '#E91E63',
        },
        volunteerButtonOutline: {
            backgroundColor: 'transparent',
            borderColor: '#E91E63',
        },
        volunteerButtonOutlineDashed: {
            backgroundColor: 'transparent',
            borderWidth: 2,
            borderColor: '#E91E63',
            borderStyle: 'dashed',
        },
        volunteerButtonText: {
            color: '#FFFFFF',
        },
        volunteerButtonTextOutline: {
            color: '#E91E63',
        },
    });

    // Enhanced button display logic with strict role exclusivity
    let shouldShowParticipantButton = false;
    let shouldShowVolunteerButton = false;
    
    if (isRegistered) {
        // User is already registered as participant -> ONLY show participant button
        shouldShowParticipantButton = true;
        shouldShowVolunteerButton = false;
    } else if (isVolunteerApplied) {
        // User has volunteer application (any status) -> ONLY show volunteer button
        shouldShowParticipantButton = false;
        shouldShowVolunteerButton = true;
    } else {
        // User has no role yet -> show available options based on permissions
        shouldShowParticipantButton = true; // Always show participant button (including when full)
        shouldShowVolunteerButton = !!hasVolunteerPermission; // Only those with volunteer permission
    }

    return (
        <>
            <View style={styles.container}>
                <View style={styles.buttonContainer}>
                    {/* Participant Button - always takes up half space */}
                    {shouldShowParticipantButton && (
                        <TouchableOpacity
                            style={[
                                styles.button,
                                { flex: 1 }, // Always take space (full width if alone, half if with volunteer)
                                isRegistered && styles.buttonOutline,
                                (!isAuthenticated || isRegistered) ? null :
                                    (isEventFull && !hasWaitlistSpace) ? styles.buttonDisabled :
                                    (isEventFull && hasWaitlistSpace) ? styles.buttonWaitlist : null,
                                isButtonLoading && styles.buttonDisabled,
                            ]}
                            onPress={handleParticipantButtonPress}
                            disabled={isButtonLoading || (!isAuthenticated || isRegistered ? false : isEventFull && !hasWaitlistSpace)}
                            activeOpacity={0.8}
                        >
                            {isButtonLoading ? (
                                <View style={styles.loadingContainer}>
                                    <ActivityIndicator 
                                        size="small" 
                                        color={
                                            isRegistered ? theme.colors.primary :
                                            (!isAuthenticated || isRegistered) ? theme.colors.onPrimary :
                                                (isEventFull && !hasWaitlistSpace) ? theme.system.secondaryText : theme.colors.onPrimary
                                        } 
                                    />
                                    <Text style={[
                                        styles.buttonText, 
                                        isRegistered && styles.buttonTextOutline,
                                        (!isAuthenticated || isRegistered) ? null :
                                            (isEventFull && !hasWaitlistSpace) ? styles.buttonTextDisabled : null
                                    ]}>
                                        {t('common.loading')}
                                    </Text>
                                </View>
                            ) : (
                                <Text style={[
                                    styles.buttonText,
                                    isRegistered && styles.buttonTextOutline,
                                    (!isAuthenticated || isRegistered) ? null :
                                        (isEventFull && !hasWaitlistSpace) ? styles.buttonTextDisabled : null
                                ]}>
                                    {getParticipantButtonText()}
                                </Text>
                            )}
                        </TouchableOpacity>
                    )}
                    
                    {/* Volunteer Button - always takes up half space */}
                    {shouldShowVolunteerButton && (
                        <TouchableOpacity
                            style={[
                                styles.button,
                                { flex: 1 }, // Always take half space
                                isVolunteerPending ? styles.buttonDisabled :
                                canCancelVolunteerApplication ? styles.volunteerButtonOutlineDashed : styles.volunteerButtonOutline,
                                isButtonLoading && styles.buttonDisabled,
                            ]}
                            onPress={handleVolunteerButtonPress}
                            disabled={isButtonLoading || isVolunteerPending}
                            activeOpacity={0.8}
                        >
                            {isButtonLoading ? (
                                <View style={styles.loadingContainer}>
                                    <ActivityIndicator 
                                        size="small" 
                                        color="#E91E63" 
                                    />
                                    <Text style={[styles.buttonText, styles.volunteerButtonTextOutline]}>
                                        {t('common.loading')}
                                    </Text>
                                </View>
                            ) : (
                                <View style={styles.loadingContainer}>
                                    <MaterialCommunityIcons 
                                        name={
                                            isVolunteerPending ? "clock-outline" :
                                            canCancelVolunteerApplication ? "account-remove" : "account-heart"
                                        } 
                                        size={20} 
                                        color={
                                            isVolunteerPending ? theme.system.secondaryText :
                                            "#E91E63"
                                        } 
                                    />
                                    <Text style={[
                                        styles.buttonText, 
                                        isVolunteerPending ? styles.buttonTextDisabled : styles.volunteerButtonTextOutline
                                    ]}>
                                        {getVolunteerButtonText()}
                                    </Text>
                                </View>
                            )}
                        </TouchableOpacity>
                    )}
                </View>
            </View>

            {/* Success/Error Dialog */}
            <CustomDialog
                visible={dialogVisible}
                type={dialogType}
                title={dialogTitle}
                message={dialogMessage}
                confirmText={t('common.ok')}
                onConfirm={() => setDialogVisible(false)}
            />
            
            {/* Confirmation Dialog */}
            <CustomDialog
                visible={confirmDialogVisible}
                type="warning"
                title={confirmDialogTitle}
                message={confirmDialogMessage}
                confirmText={t('common.confirm')}
                cancelText={t('common.cancel')}
                onConfirm={handleConfirmAction}
                onCancel={handleCancelAction}
            />
            
            {/* Verification Requirements Dialog */}
            <CustomDialog
                visible={verificationDialogVisible}
                type="warning"
                title={t('events.detail.verificationRequiredTitle')}
                confirmText={t('events.detail.goToVerification')}
                cancelText={t('common.cancel')}
                onConfirm={() => {
                    setVerificationDialogVisible(false);
                    router.push('/user-profile/identification/IdentifyStatusScreen');
                }}
                onCancel={() => setVerificationDialogVisible(false)}
                customContent={
                    <View style={{ alignItems: 'flex-start', width: '100%', marginBottom: 8 }}>
                        <Text style={{
                            fontSize: 16,
                            marginBottom: 16,
                            lineHeight: 22,
                            textAlign: 'center',
                            width: '100%'
                        }}>
                            {t('events.detail.pleaseCompleteVerification')}
                        </Text>
                        
                        <Text style={{
                            fontSize: 16,
                            color: theme.system.text,
                            fontWeight: '600',
                            marginBottom: 8,
                            alignSelf: 'flex-start'
                        }}>
                            {t('events.detail.missingVerifications')}:
                        </Text>
                        
                        {missingVerificationsList.map((type, index) => (
                            <View key={index} style={{
                                flexDirection: 'row',
                                alignItems: 'flex-start',
                                marginBottom: 6,
                                paddingLeft: 8,
                                width: '100%'
                            }}>
                                <Text style={{
                                    fontSize: 16,
                                    color: theme.system.text,
                                    marginRight: 8,
                                }}>•</Text>
                                <Text style={{
                                    fontSize: 16,
                                    color: theme.system.text,
                                    flex: 1,
                                    textAlign: 'left'
                                }}>
                                    {t(`events.detail.identityRequired.${type}`)}
                                </Text>
                            </View>
                        ))}
                    </View>
                }
            />
        </>
    );
};

export default EventActionButtons; 