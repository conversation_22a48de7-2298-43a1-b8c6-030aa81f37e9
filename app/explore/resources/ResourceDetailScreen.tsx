import React, { useLayoutEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  Linking,
  Share,
  Alert,
} from 'react-native';
import { useRouter, useLocalSearchParams, Stack } from 'expo-router';
import { useNavigation } from '@react-navigation/native';
import MaterialCommunityIcons from '@expo/vector-icons/MaterialCommunityIcons';
import { useTranslation } from 'react-i18next';
import { format, parseISO } from 'date-fns';
import { toZonedTime } from 'date-fns-tz';
import { ErrorView } from '@/common_modules/ErrorView';
import { appStyleStore } from 'stores/app_style_store';
import { createTheme } from 'theme/index';
import { ResourceListPayload, ResourceFilePayload } from '@/api/api_config';

// Define AppTheme based on the return type of createTheme
type AppTheme = ReturnType<typeof createTheme>;

interface FileItemProps {
  file: ResourceFilePayload;
  styles: any;
  t: (key: string) => string;
  activeTheme: AppTheme;
}

const FileItem = ({ file, styles, t, activeTheme }: FileItemProps) => {
  const getFileIcon = (type: string) => {
    switch (type.toLowerCase()) {
      case 'pdf': return 'file-pdf-box';
      case 'doc': case 'docx': return 'file-word';
      case 'xls': case 'xlsx': return 'file-excel';
      case 'zip': case 'rar': return 'folder-zip';
      case 'ppt': case 'pptx': return 'file-powerpoint';
      case 'jpg': case 'jpeg': case 'png': return 'file-image';
      case 'mp4': case 'video': return 'file-video';
      case 'mp3': case 'audio': return 'file-music';
      default: return 'file-document-outline';
    }
  };

  const getFileIconColor = (type: string) => {
    switch (type.toLowerCase()) {
      case 'pdf': return '#F40F02';
      case 'doc': case 'docx': return '#4F8CC9';
      case 'xls': case 'xlsx': return '#1D6F42';
      case 'zip': case 'rar': return '#FFA940';
      case 'ppt': case 'pptx': return '#D24726';
      case 'jpg': case 'jpeg': case 'png': return '#0078D7';
      case 'mp4': case 'video': return '#FF4081';
      case 'mp3': case 'audio': return '#6200EA';
      default: return activeTheme.system.secondaryText;
    }
  };

  const formatFileSize = (bytes: number): string => {
    if (bytes === 0) return '0 B';
    const k = 1024;
    const sizes = ['B', 'KB', 'MB', 'GB', 'TB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return `${parseFloat((bytes / Math.pow(k, i)).toFixed(2))} ${sizes[i]}`;
  };

  const handleDownload = async (url: string) => {
    try {
      const supported = await Linking.canOpenURL(url);
      if (supported) {
        await Linking.openURL(url);
      } else {
        Alert.alert(t('common.error'), t('resources.cannotOpenUrl'));
      }
    } catch (err) {
      Alert.alert(t('common.error'), t('resources.downloadError'), [{ text: t('common.ok') }]);
    }
  };

  return (
    <View style={styles.fileCard}>
      <View style={styles.fileHeader}>
        <MaterialCommunityIcons
          name={getFileIcon(file.file_type)}
          size={24}
          color={getFileIconColor(file.file_type)}
          style={styles.fileIcon}
        />
        <View style={styles.fileInfo}>
          <Text style={styles.fileName} numberOfLines={1}>
            {file.file_name}
          </Text>
          <Text style={styles.fileSize}>{formatFileSize(file.file_size)}</Text>
        </View>
        <TouchableOpacity
          onPress={() => handleDownload(file.file_path)}
          style={styles.downloadButton}
        >
          <MaterialCommunityIcons
            name="download"
            size={24}
            color={activeTheme.colors.primary}
          />
        </TouchableOpacity>
      </View>
    </View>
  );
};

export function ResourceDetailScreen() {
  const { t, i18n } = useTranslation();
  const storeTheme = appStyleStore(state => state.theme);
  const activeTheme = storeTheme || createTheme('red');

  const router = useRouter();
  const params = useLocalSearchParams<{ resourceId: string, resourceOrgId?: string, resourceData?: string }>();
  const navigation = useNavigation();

  // Removed getLocale function as we now use unified date format

  const formatResourceDate = (dateString: string | undefined): string => {
    if (!dateString) return '';
    try {
      const timeZone = 'Asia/Hong_Kong';
      const parsedDate = parseISO(dateString);
      const zonedDate = toZonedTime(parsedDate, timeZone);
      return format(zonedDate, 'yyyy-MM-dd HH:mm');
    } catch (error) {
      console.warn('Error formatting date:', error);
      return '';
    }
  };

  // Use passed resource data only
  const resource = React.useMemo(() => {
    if (params.resourceData) {
      try {
        const parsed = JSON.parse(params.resourceData) as ResourceListPayload;
        console.log('[ResourceDetailScreen] Using passed resource data:', parsed.id, parsed.title);
        return parsed;
      } catch (error) {
        console.warn('[ResourceDetailScreen] Failed to parse passed resource data:', error);
        return null;
      }
    }
    console.log('[ResourceDetailScreen] No passed resource data available');
    return null;
  }, [params.resourceData]);

  const styles = getStyles(activeTheme);

  useLayoutEffect(() => {
    navigation.setOptions({
      title: t('resources.title'),
    });
  }, [navigation, t, i18n.language]);

  const handleDownload = async (url: string) => {
    try {
      const supported = await Linking.canOpenURL(url);
      if (supported) {
        await Linking.openURL(url);
      } else {
        Alert.alert(t('common.error'), t('resources.cannotOpenUrl'));
      }
    } catch (err) {
      Alert.alert(t('common.error'), t('resources.downloadError'), [{ text: t('common.ok') }]);
    }
  };

  const handleShare = async () => {
    if (!resource) return;
    try {
      await Share.share({
        message: `${resource.title}\n${formatResourceDate(resource.updated_at)}`,
        title: resource.title,
      });
    } catch (shareError) {
      // No console.error here
    }
  };

  // If no resource data, show error view
  if (!resource) {
    return (
      <ErrorView
        onGoBack={() => router.back()}
      />
    );
  }

  return (
    <>
      <Stack.Screen
        options={{
          headerTitle: t('resources.title'),
          headerRight: () => (
            <TouchableOpacity onPress={handleShare} style={styles.headerButton}>
              <MaterialCommunityIcons name="share-variant" size={24} color={activeTheme.system.text} />
            </TouchableOpacity>
          )
        }}
      />
      <View style={styles.container}>
        <ScrollView style={styles.scrollView} bounces={false} showsVerticalScrollIndicator={false}>
          <View style={styles.content}>
            <Text style={styles.title}>{resource.title}</Text>

            <View style={styles.metaInfo}>
              <View style={styles.metaItem}>
                <MaterialCommunityIcons name="file-multiple-outline" size={16} color={activeTheme.system.secondaryText} />
                <Text style={styles.metaText}>
                  {resource.files && resource.files.length > 0
                    ? resource.files.length > 1
                      ? `${resource.files.length}${t('resources.files')}`
                      : `1${t('resources.file')}`
                    : `0${t('resources.files')}`}
                </Text>
              </View>
              <View style={styles.metaItem}>
                <MaterialCommunityIcons name="clock-outline" size={16} color={activeTheme.system.secondaryText} />
                <Text style={styles.metaText}>
                  {formatResourceDate(resource.updated_at)}
                </Text>
              </View>
            </View>

            {resource.description && (
              <View style={styles.descriptionSection}>
                <Text style={styles.sectionTitle}>{t('resources.description')}</Text>
                <Text style={styles.descriptionBody}>{resource.description}</Text>
              </View>
            )}

            {resource.files && resource.files.length > 0 && (
              <View style={styles.filesSection}>
                <Text style={styles.sectionTitle}>{t('resources.attachment')}</Text>
                {resource.files.map((file: ResourceFilePayload) => (
                  <FileItem key={file.id} file={file} styles={styles} t={t} activeTheme={activeTheme} />
                ))}
              </View>
            )}
          </View>
        </ScrollView>
      </View>
    </>
  );
}

const getStyles = (theme: AppTheme) => StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: theme.colors.background,
  },
  scrollView: {
    flex: 1,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: theme.colors.background,
  },
  errorContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 20,
    backgroundColor: theme.colors.background,
  },
  errorText: {
    fontSize: 16,
    color: theme.system.secondaryText,
    textAlign: 'center',
    marginTop: 16,
    marginBottom: 24,
  },
  goBackButton: {
    paddingVertical: 10,
    paddingHorizontal: 20,
    borderRadius: 8,
    borderWidth: 1,
    borderColor: theme.colors.primary,
  },
  goBackButtonText: {
    fontSize: 16,
    fontWeight: '600',
  },
  headerButton: {
    paddingHorizontal: 16,
  },
  content: {
    padding: 16,
  },
  title: {
    fontSize: 24,
    fontWeight: 'bold',
    color: theme.system.text,
    marginBottom: 12,
  },
  metaInfo: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    alignItems: 'center',
    marginBottom: 16,
    paddingBottom: 12,
    borderBottomWidth: StyleSheet.hairlineWidth,
    borderBottomColor: theme.system.border,
  },
  metaItem: {
    flexDirection: 'row',
    alignItems: 'center',
    marginRight: 16,
    marginBottom: 8,
  },
  metaText: {
    fontSize: 14,
    color: theme.system.secondaryText,
    marginLeft: 6,
  },
  descriptionSection: {
    marginBottom: 20,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: theme.system.text,
    marginBottom: 12,
  },
  descriptionBody: {
    fontSize: 15,
    lineHeight: 22,
    color: theme.system.text,
  },
  filesSection: {
    marginTop: 16,
  },
  fileCard: {
    backgroundColor: theme.system.border,
    borderRadius: 8,
    padding: 12,
    marginBottom: 10,
  },
  fileHeader: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  fileIcon: {
    marginRight: 12,
  },
  fileInfo: {
    flex: 1,
    marginRight: 8,
  },
  fileName: {
    fontSize: 15,
    fontWeight: '500',
    color: theme.system.text,
  },
  fileSize: {
    fontSize: 13,
    color: theme.system.secondaryText,
    marginTop: 2,
  },
  downloadButton: {
    padding: 4,
  },
  fileDescription: {
    marginTop: 8,
    paddingTop: 8,
    borderTopWidth: StyleSheet.hairlineWidth,
    borderTopColor: theme.system.border,
  },
  descriptionText: {
    fontSize: 14,
    color: theme.system.secondaryText,
    fontStyle: 'italic',
  },
  closeButton: {
    marginLeft: 10,
    padding: 5,
  },
});

export default ResourceDetailScreen;
