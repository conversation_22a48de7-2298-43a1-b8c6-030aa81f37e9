# THE Moment

## Development Setup

### Prerequisites
- Node.js and npm
- Expo CLI (`npm install -g expo-cli`)
- EAS CLI (`npm install -g eas-cli`)

### Install Dependencies
```bash
npm ci
```

### Environment Setup
```bash
# Copy the environment template
cp .env.example .env

# Edit .env with your specific configuration values
# The file contains development defaults that should work out of the box
```

### Start Development Server
```bash
npm run start
```

## Building the App

### Local Builds (No EAS Credits Required)

#### Prerequisites for Local Builds
- **iOS**: Xcode and Xcode Command Line Tools
- **Android**: Android Studio with Android SDK and Java Development Kit (JDK)

#### Build Commands
```bash
# Build for current platform
npm run build:local

# Build for specific platform
npm run build:local:ios
npm run build:local:android

# Build with specific profile
npm run build:local:dev
npm run build:local:preview
```

#### iOS Simulator Setup
1. **Build for iOS Simulator**:
   ```bash
   npm run build:local:dev -- --platform ios
   ```

2. **Install and Run on Simulator**:
   ```bash
   # List available simulators
   xcrun simctl list devices
   
   # Install the built app on simulator
   xcrun simctl install booted /path/to/your/app.app
   
   # Launch the app
   xcrun simctl launch booted com.hexacubic.themoment
   ```

3. **Alternative: Use Expo Development Build**:
   ```bash
   # Start development server
   npm run start
   
   # Press 'i' to run on iOS simulator
   # The development build will be installed automatically
   ```

### Cloud Builds (Uses EAS Credits)
```bash
# Build on EAS (uses credits)
npx eas build

# Build for specific platform
npx eas build --platform ios
npx eas build --platform android

# Build with specific profile
npx eas build --profile development
npx eas build --profile preview
npx eas build --profile production
```

## Environment Variables for EAS Builds

### Setting Environment Variables for EAS
For production and preview builds, you need to configure environment variables in EAS:

#### Method 1: Individual Commands
```bash
# Set environment variables for EAS builds
npx eas secret:create --scope project --name EXPO_PUBLIC_API_BASE --value "https://your-api-url.com/api/v1"
npx eas secret:create --scope project --name EXPO_PUBLIC_MEDIA_BASE --value "https://your-api-url.com"
```

#### Method 2: Bulk Upload from .env File
```bash
# Upload all variables from a .env file
npx eas secret:push --scope project --env-file .env.production

# Or for specific environment
npx eas secret:push --scope project --env-file .env.preview
```

#### Method 3: Using Shell Script for Multiple Variables
```bash
# Create a script to set multiple secrets
#!/bin/bash
npx eas secret:create --scope project --name EXPO_PUBLIC_API_BASE --value "https://production-api.com/api/v1"
npx eas secret:create --scope project --name EXPO_PUBLIC_MEDIA_BASE --value "https://production-api.com"
# Add more variables as needed
```

#### Managing Secrets
```bash
# View all configured secrets
npx eas secret:list

# Delete a secret (if needed)
npx eas secret:delete --name SECRET_NAME

# Delete all secrets (use with caution)
npx eas secret:delete --all
```

### Environment-Specific Variables
The app uses different configurations for each build profile defined in `eas.json`:

- **Development**: Uses local `.env` file and `APP_ENV=development`
- **Preview**: Uses EAS secrets with `APP_ENV=preview`  
- **Production**: Uses EAS secrets with `APP_ENV=production`

### Required Environment Variables for EAS
Make sure to set these variables for production builds:

```bash
# API Configuration
npx eas secret:create --scope project --name EXPO_PUBLIC_API_BASE --value "https://production-api.com/api/v1"
npx eas secret:create --scope project --name EXPO_PUBLIC_MEDIA_BASE --value "https://production-api.com"

# Add other production-specific variables as needed
```

The `APP_ENV` variable is automatically set in `eas.json` for each build profile and doesn't need to be manually configured.

Expo EAS:
<EMAIL>
https://expo.dev/accounts/hexacubicltd/projects/the-moment

Play Console:
<EMAIL>
https://play.google.com/console/u/0/developers/6222265345161980546/app/4972380534467987168/app-dashboard

Apple Developer:
<EMAIL>
https://appstoreconnect.apple.com/apps/**********/distribution/ios/version/inflight


### Screenshot devices
6.9" iPhone: 16 Plus
13" iPad: Air (M3)
Android Phone: Simulator medium phone