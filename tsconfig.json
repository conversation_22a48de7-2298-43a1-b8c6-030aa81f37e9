{
  "extends": "expo/tsconfig.base",
  "compilerOptions": {
    "strict": true,
    "resolveJsonModule": true,
    "baseUrl": ".",
    "paths": {
      "@/api/*": ["api/*"],
      "@/assets/*": ["assets/*"],
      "@/common_modules/*": ["common_modules/*"],
      "@/config/*": ["config/*"],
      "@/i18n/*": ["i18n/*"],
      "@/stores/*": ["stores/*"],
      "@/theme/*": ["theme/*"],
      "@/types/*": ["types/*"],
      "@/utils/*": ["utils/*"],
      "@/app/*": ["app/*"],
      "@/documents/*": ["app/documents/*"],
      "@/explore/*": ["app/explore/*"],
      "@/login/*": ["app/login/*"],
      "@/tabs/*": ["app/tabs/*"],
      "@/user-events/*": ["app/user-events/*"],
      "@/user-profile/*": ["app/user-profile/*"],
    }
  },
  "include": [
    "**/*.ts",
    "**/*.tsx",
    "**/*.json"
  ]
}
