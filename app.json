{"expo": {"name": "THE Moment", "slug": "the-moment", "scheme": "themoment", "version": "1.0.0", "orientation": "portrait", "icon": "./assets/default-images/default-logo.png", "userInterfaceStyle": "light", "newArchEnabled": true, "splash": {"image": "./assets/default-images/default-logo.png", "resizeMode": "contain", "backgroundColor": "#ffffff"}, "ios": {"bundleIdentifier": "com.hexacubic.themoment", "supportsTablet": true, "infoPlist": {"ITSAppUsesNonExemptEncryption": false, "NSCameraUsageDescription": "Camera access is required to take photos for document verification", "NSCalendarsUsageDescription": "Calendar access is required to add joined events to your calendar", "NSCalendarsWriteOnlyAccessUsageDescription": "Calendar write access is required to add joined events to your calendar", "NSCalendarsFullAccessUsageDescription": "Full calendar access is required to add and manage joined events in your calendar"}}, "android": {"package": "com.hexacubic.themoment", "adaptiveIcon": {"foregroundImage": "./assets/default-images/default-logo.png", "backgroundColor": "#ffffff"}, "permissions": ["android.permission.CAMERA", "android.permission.READ_CALENDAR", "android.permission.WRITE_CALENDAR"]}, "web": {"favicon": "./assets/favicon.png"}, "plugins": ["expo-router", "expo-video", ["expo-calendar", {"calendarPermission": "Calendar access is required to add joined events to your calendar"}]], "extra": {"router": {}, "eas": {"projectId": "98f7f685-9c49-419a-8957-acbfc0b4bce0"}, "APP_ENV": "development", "APP_DEBUG": "true", "APP_VERSION": "1.0.0", "API_TIMEOUT": "30000", "API_REFETCH_INTERVAL": "60000", "AUTH_CLIENT_ID": "mobile-app-client", "AUTH_REDIRECT_URI": "themoment-dev://auth", "AUTH_REFRESH_THRESHOLD_MINUTES": "5", "DEFAULT_LANGUAGE": "zh-HK", "FALLBACK_LANGUAGE": "en", "I18N_DEBUG": "true", "FEATURE_ENABLE_ANALYTICS": "false", "FEATURE_ENABLE_PUSH_NOTIFICATIONS": "true", "FEATURE_ENABLE_BIOMETRIC_AUTH": "false", "FEATURE_ENABLE_DEBUG_MENU": "true", "FEATURE_MOCK_MODE": "false", "DEFAULT_THEME_COLOR": "red", "ANIMATION_SPEED_MULTIPLIER": "1.0", "ENABLE_HAPTIC_FEEDBACK": "true", "FONT_SCALE_FACTOR": "1.0", "ENABLE_REACT_QUERY_DEVTOOLS": "true", "ENABLE_STATE_DEVTOOLS": "true", "ENABLE_NETWORK_LOGGING": "true", "ENABLE_FLIPPER": "true", "ENABLE_PERFORMANCE_MONITORING": "false", "STORAGE_KEY_PREFIX": "themoment_", "CACHE_DURATION_MINUTES": "60", "CACHE_MAX_SIZE_MB": "100", "ENABLE_CERTIFICATE_PINNING": "false", "ENABLE_SSL_VERIFICATION": "true", "REQUIRE_BIOMETRIC_AUTH": "false", "SESSION_TIMEOUT_MINUTES": "60", "DEFAULT_ENABLE_APP_NOTIFICATIONS": "true", "DEFAULT_ENABLE_WHATSAPP_NOTIFICATIONS": "true", "TEST_MODE": "false", "MOCK_API_RESPONSES": "false", "LOG_LEVEL": "debug", "ENABLE_REMOTE_LOGGING": "false", "IMAGE_CACHE_SIZE_MB": "50", "MAX_CONCURRENT_REQUESTS": "6", "NETWORK_RETRY_ATTEMPTS": "3", "ENABLE_ACCESSIBILITY_FEATURES": "true", "DEFAULT_FONT_SIZE": "medium", "ENABLE_HIGH_CONTRAST": "false", "ENABLE_REDUCED_MOTION": "false", "PRIVACY_POLICY_URL": "https://example.com/privacy", "TERMS_OF_SERVICE_URL": "https://example.com/terms", "EXPO_USE_UPDATES": "true"}}}