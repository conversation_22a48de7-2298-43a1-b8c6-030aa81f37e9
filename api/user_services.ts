import {
  useQuery,
  useMutation,
  useQueryClient,
  type UseQueryOptions, // Ensure UseQueryOptions is imported
  type QueryKey, // Import QueryKey
  // type UseMutationOptions, // Not strictly needed if options are inline
} from '@tanstack/react-query';
import axiosInstance from './axios_instance';
import { AxiosError } from 'axios';
import axios from 'axios';
import { authenticationStore } from '@/stores/authentication_store';
import { API_BASE_URL, ApiConfig, type AccessTokenRefreshResponse } from '@/api/api_config';
import {
  fetchIntervalMs,
  type ProfilePullRequest,
  type ProfilePushRequest,
  type ProfileUploadIconRequest,
  type ChangePhoneOtpInitiateRequest,
  type ChangePhoneOtpVerifyRequest,
  type OrganizationListPullRequest,
  type StatisticsPullRequest,
  type UserIdPullRequest,
  type VerificationsPullRequest,
  type VerificationsPushRequest,
  type VerificationsDetailsPullRequest,
  type VolunteerOrganizationQualificationsPullRequest,
  type ProfileResponse,
  type PhoneOtpInitiateResponse,
  type ChangePhoneOtpVerifyResponse,
  type OrganizationListPayload,
  type StatisticsPullResponse,
  type UserIdPullResponse,
  type VerificationPayload,
  type VolunteerQualificationPayload,
} from '@/api/api_config';
import {
  userProfileStore,
  userOrganizationsStore,
  userStatisticsStore,
  userIdStore,
  userVerificationsStore,
  userVerificationDetailsStore,
  userVolunteerQualificationsStore,
} from 'stores/user_store';
import { useEffect } from 'react';
import * as Crypto from 'expo-crypto';
import { encode as btoa } from 'base-64';
import { handleAuthenticationError } from '@/utils/authUtils';

// General helper function to handle API errors, including invalid auth tokens
const handleApiError = async (
  error: unknown,
  queryClient: ReturnType<typeof useQueryClient>,
  contextLocation: string,
  requestPayload: any,
  queryKey: QueryKey | readonly unknown[]
) => {
  if (axios.isAxiosError(error)) {
    const axiosError = error as AxiosError;
    if (axiosError.response?.status === 401) {
      await handleAuthenticationError(queryClient);
    }
  }
  // More generic error logging
  console.error(`[${contextLocation}] Error:`, error);
  console.error(`[${contextLocation}] Request payload:`, requestPayload);
  console.error(`[${contextLocation}] Query key:`, queryKey);
};

// Reusable PKCE Challenge Generator
export const generatePKCEChallenge = async (): Promise<{ codeVerifier: string; codeChallenge: string }> => {
  // Generate code verifier (random 43-128 characters)
  const codeVerifier = btoa(
    String.fromCharCode(...new Uint8Array(await Crypto.getRandomBytesAsync(32)))
  )
    .replace(/\+/g, '-')
    .replace(/\//g, '_')
    .replace(/=/g, '');

  // Generate code challenge (SHA256 hash of verifier)
  const encoder = new TextEncoder();
  const data = encoder.encode(codeVerifier);
  const digest = await Crypto.digest(Crypto.CryptoDigestAlgorithm.SHA256, data);

  // Convert ArrayBuffer to base64url
  const codeChallenge = btoa(
    String.fromCharCode(...new Uint8Array(digest))
  )
    .replace(/\+/g, '-')
    .replace(/\//g, '_')
    .replace(/=/g, '');

  return { codeVerifier, codeChallenge };
};

// Helper function to handle token refresh
const handleTokenRefresh = async (refreshToken: string): Promise<AccessTokenRefreshResponse> => {
  const response = await axios.post<AccessTokenRefreshResponse>(
    `${API_BASE_URL}${ApiConfig.authentication.refresh_token.endpoint}`,
    { refresh_token: refreshToken },
    { headers: { 'Content-Type': 'application/json' } }
  );
  return response.data;
};

// Fetch User Profile
export const useFetchUserProfile = (requestParams?: ProfilePullRequest, enabled: boolean = true) => {
  const queryClient = useQueryClient();
  const isAuthenticated = authenticationStore(state => state.isAuthenticated);
  const queryKeyConst = ['userProfile', requestParams] as const;

  const queryResult = useQuery<ProfileResponse, Error, ProfileResponse, typeof queryKeyConst>({
    queryKey: queryKeyConst,
    queryFn: async () => {
      try {
        const response = await axiosInstance.request<ProfileResponse>({
          url: ApiConfig.user.profile_pull.endpoint,
          method: ApiConfig.user.profile_pull.method,
          headers: { 'Content-Type': 'application/json' },
        });
        return response.data;
      } catch (error) {
        throw error;
      }
    },
    refetchInterval: fetchIntervalMs,
    enabled: isAuthenticated && enabled, // Only fetch when user is authenticated and enabled
  });

  useEffect(() => {
    const store = userProfileStore.getState();
    if (queryResult.isLoading) {
      store.setIsFetching(true);
    } else if (queryResult.isError && queryResult.error) {
      // Token handling is now managed by axios interceptor
      store.setError(queryResult.error as any);
    } else if (queryResult.isSuccess && queryResult.data) {
      store.setProfile(queryResult.data);
      store.setIsFetching(false);
      store.setError(null);
    }
  }, [queryResult.isLoading, queryResult.isError, queryResult.isSuccess, queryResult.data, queryResult.error, requestParams]); // Added requestParams

  return queryResult;
};

// Update User Profile
export const useUpdateUserProfile = () => {
  const queryClient = useQueryClient();

  const mutationResult = useMutation<ProfileResponse, Error, ProfilePushRequest>({
    mutationFn: async (profileData: ProfilePushRequest) => {
      const response = await axiosInstance.request<ProfileResponse>({
        url: ApiConfig.user.profile_push.endpoint,
        method: ApiConfig.user.profile_push.method,
        data: profileData,
        headers: { 'Content-Type': 'application/json' },
      });
      return response.data;
    },
    onSuccess: (data) => {
      // Update the store with the new profile data
      userProfileStore.getState().setProfile(data);
      // Invalidate and refetch user profile query
      queryClient.invalidateQueries({ queryKey: ['userProfile'] });
    },
    onError: (error: Error) => {
      userProfileStore.getState().setError(error as any);
      handleApiError(error, queryClient, 'user_services (useUpdateUserProfile)', null, ['userProfile']);
    },
  });

  return mutationResult;
};

// Upload Profile Picture
export const useUploadProfilePicture = () => {
  const queryClient = useQueryClient();

  const mutationResult = useMutation<ProfileResponse, Error, ProfileUploadIconRequest>({
    mutationFn: async (uploadData: ProfileUploadIconRequest) => {
      const formData = new FormData();
      formData.append('profile_picture', {
        uri: uploadData.profile_picture.uri,
        type: uploadData.profile_picture.type,
        name: uploadData.profile_picture.name,
      } as any);

      const response = await axiosInstance.request<ProfileResponse>({
        url: ApiConfig.user.profile_upload_icon.endpoint,
        method: ApiConfig.user.profile_upload_icon.method,
        data: formData,
        headers: {
          'Content-Type': 'multipart/form-data',
        },
      });
      return response.data;
    },
    onSuccess: (data) => {
      // Update the store with the new profile data
      userProfileStore.getState().setProfile(data);
      // Invalidate and refetch user profile query
      queryClient.invalidateQueries({ queryKey: ['userProfile'] });
    },
    onError: (error: Error) => {
      userProfileStore.getState().setError(error as any);
      handleApiError(error, queryClient, 'user_services (useUploadProfilePicture)', null, ['userProfile']);
    },
  });

  return mutationResult;
};

// Initiate Phone Number Change
export const useInitiatePhoneNumberChange = () => {
  const queryClient = useQueryClient();

  const mutationResult = useMutation<PhoneOtpInitiateResponse, Error, ChangePhoneOtpInitiateRequest>({
    mutationFn: async (phoneData: ChangePhoneOtpInitiateRequest) => {
      const response = await axiosInstance.request<PhoneOtpInitiateResponse>({
        url: ApiConfig.user.change_phone_otp_initiate.endpoint,
        method: ApiConfig.user.change_phone_otp_initiate.method,
        data: phoneData,
        headers: { 'Content-Type': 'application/json' },
      });
      return response.data;
    },
    onError: (error: Error) => {
      handleApiError(error, queryClient, 'user_services (useInitiatePhoneNumberChange)', null, []);
    },
  });

  return mutationResult;
};

// Verify Phone Number Change
export const useVerifyPhoneNumberChange = () => {
  const queryClient = useQueryClient();

  const mutationResult = useMutation<ChangePhoneOtpVerifyResponse, Error, ChangePhoneOtpVerifyRequest>({
    mutationFn: async (verifyData: ChangePhoneOtpVerifyRequest) => {
      const response = await axiosInstance.request<ChangePhoneOtpVerifyResponse>({
        url: ApiConfig.user.change_phone_otp_verify.endpoint,
        method: ApiConfig.user.change_phone_otp_verify.method,
        data: verifyData,
        headers: { 'Content-Type': 'application/json' },
      });
      return response.data;
    },
    onSuccess: () => {
      // Invalidate and refetch user profile to get updated phone number
      queryClient.invalidateQueries({ queryKey: ['userProfile'] });
    },
    onError: (error: Error) => {
      handleApiError(error, queryClient, 'user_services (useVerifyPhoneNumberChange)', null, []);
    },
  });

  return mutationResult;
};

// Fetch User Organizations
export const useFetchUserOrganizations = (requestParams?: OrganizationListPullRequest, enabled: boolean = true) => {
  const queryClient = useQueryClient();
  const isAuthenticated = authenticationStore((state) => state.isAuthenticated);
  const queryKeyConst = ['userOrganizations', requestParams] as const;

  const queryResult = useQuery<OrganizationListPayload[], Error, OrganizationListPayload[], typeof queryKeyConst>({
    queryKey: queryKeyConst,
    queryFn: async () => {
      try {
        const response = await axiosInstance.request<OrganizationListPayload[]>({
          url: ApiConfig.user.organization_list_pull.endpoint,
          method: ApiConfig.user.organization_list_pull.method,
          headers: { 'Content-Type': 'application/json' },
        });
        return response.data;
      } catch (error) {
        throw error;
      }
    },
    enabled: isAuthenticated && enabled, // Only fetch when user is authenticated and enabled
  });

  useEffect(() => {
    const store = userOrganizationsStore.getState();
    if (queryResult.isLoading) {
      store.setIsFetching(true);
    } else if (queryResult.isError && queryResult.error) {
      store.setError(queryResult.error as any);
      // Token handling is now managed by axios interceptor
    } else if (queryResult.isSuccess && queryResult.data) {
      store.setOrganizations(queryResult.data);
      store.setIsFetching(false);
      store.setError(null);
    }
  }, [queryResult.isLoading, queryResult.isError, queryResult.isSuccess, queryResult.data, queryResult.error, requestParams]); // Added requestParams

  return queryResult;
};

// Fetch User Statistics
export const useFetchUserStatistics = (requestParams?: StatisticsPullRequest, enabled: boolean = true) => {
  const queryClient = useQueryClient();
  const isAuthenticated = authenticationStore((state) => state.isAuthenticated);
  const queryKeyConst = ['userStatistics', requestParams] as const;

  const queryResult = useQuery<StatisticsPullResponse, Error, StatisticsPullResponse, typeof queryKeyConst>({
    queryKey: queryKeyConst,
    queryFn: async () => {
      try {
        const response = await axiosInstance.request<StatisticsPullResponse>({
          url: ApiConfig.user.statistics_pull.endpoint,
          method: ApiConfig.user.statistics_pull.method,
          headers: { 'Content-Type': 'application/json' },
        });
        return response.data;
      } catch (error) {
        throw error;
      }
    },
    enabled: isAuthenticated && enabled, // Only fetch when user is authenticated and enabled
  });

  useEffect(() => {
    const store = userStatisticsStore.getState();
    if (queryResult.isLoading) {
      store.setIsFetching(true);
    } else if (queryResult.isError && queryResult.error) {
      store.setError(queryResult.error as any);
      // Token handling is now managed by axios interceptor
    } else if (queryResult.isSuccess && queryResult.data) {
      store.setStatistics(queryResult.data);
      store.setIsFetching(false);
      store.setError(null);
    }
  }, [queryResult.isLoading, queryResult.isError, queryResult.isSuccess, queryResult.data, queryResult.error, requestParams]); // Added requestParams

  return queryResult;
};

// Fetch User ID
export const useFetchUserId = (requestParams?: UserIdPullRequest, enabled: boolean = true) => {
  const queryClient = useQueryClient();
  const isAuthenticated = authenticationStore((state) => state.isAuthenticated);
  const queryKeyConst = ['userId', requestParams] as const;

  const queryResult = useQuery<UserIdPullResponse, Error, UserIdPullResponse, typeof queryKeyConst>({
    queryKey: queryKeyConst,
    queryFn: async () => {
      try {
        const response = await axiosInstance.request<UserIdPullResponse>({
          url: ApiConfig.user.user_id_pull.endpoint,
          method: ApiConfig.user.user_id_pull.method,
          headers: { 'Content-Type': 'application/json' },
        });
        return response.data;
      } catch (error) {
        throw error;
      }
    },
    enabled: isAuthenticated && enabled, // Only fetch when user is authenticated and enabled
  });

  useEffect(() => {
    const store = userIdStore.getState();
    if (queryResult.isLoading) {
      store.setIsFetching(true);
    } else if (queryResult.isError && queryResult.error) {
      store.setError(queryResult.error as any);
      // Token handling is now managed by axios interceptor
    } else if (queryResult.isSuccess && queryResult.data) {
      store.setUserId(queryResult.data);
      store.setIsFetching(false);
      store.setError(null);
    }
  }, [queryResult.isLoading, queryResult.isError, queryResult.isSuccess, queryResult.data, queryResult.error, requestParams]); // Added requestParams

  return queryResult;
};

// Fetch User Verifications
export const useFetchUserVerifications = (requestParams?: VerificationsPullRequest, enabled: boolean = true) => {
  const queryClient = useQueryClient();
  const isAuthenticated = authenticationStore((state) => state.isAuthenticated);
  const queryKeyConst = ['userVerifications', requestParams] as const;

  const queryResult = useQuery<VerificationPayload[], Error, VerificationPayload[], typeof queryKeyConst>({
    queryKey: queryKeyConst,
    queryFn: async () => {
      try {
        const response = await axiosInstance.request<VerificationPayload[]>({
          url: ApiConfig.user.verifications_pull.endpoint,
          method: ApiConfig.user.verifications_pull.method,
          headers: { 'Content-Type': 'application/json' },
        });
        return response.data;
      } catch (error) {
        throw error;
      }
    },
    enabled: isAuthenticated && enabled, // Only fetch when user is authenticated and enabled
  });

  useEffect(() => {
    const store = userVerificationsStore.getState();
    if (queryResult.isLoading) {
      store.setIsFetching(true);
    } else if (queryResult.isError && queryResult.error) {
      store.setError(queryResult.error as any);
      // Token handling is now managed by axios interceptor
    } else if (queryResult.isSuccess && queryResult.data) {
      store.setVerifications(queryResult.data);
      store.setIsFetching(false);
      store.setError(null);
    }
  }, [queryResult.isLoading, queryResult.isError, queryResult.isSuccess, queryResult.data, queryResult.error, requestParams]); // Added requestParams

  return queryResult;
};

// Submit User Verification
export const useSubmitUserVerification = () => {
  const queryClient = useQueryClient();

  const mutationResult = useMutation<VerificationPayload, Error, VerificationsPushRequest>({
    mutationFn: async (verificationData: VerificationsPushRequest) => {
      const response = await axiosInstance.request<VerificationPayload>({
        url: ApiConfig.user.verifications_push.endpoint,
        method: ApiConfig.user.verifications_push.method,
        data: verificationData,
        headers: { 'Content-Type': 'application/json' },
      });
      return response.data;
    },
    onSuccess: (data) => {
      // Update the store with the new verification
      const currentVerifications = userVerificationsStore.getState().verifications || [];
      userVerificationsStore.getState().setVerifications([...currentVerifications, data]);
      // Invalidate and refetch user verifications query
      queryClient.invalidateQueries({ queryKey: ['userVerifications'] });
    },
    onError: (error: Error) => {
      userVerificationsStore.getState().setError(error as any);
      handleApiError(error, queryClient, 'user_services (useSubmitUserVerification)', null, ['userVerifications']);
    },
  });

  return mutationResult;
};

// Fetch Verification Details
export const useFetchVerificationDetails = (requestParams: VerificationsDetailsPullRequest) => {
  const queryClient = useQueryClient();
  const { reqID } = requestParams;
  const queryKeyConst = ['verificationDetails', reqID] as const;

  const queryResult = useQuery<VerificationPayload, Error, VerificationPayload, typeof queryKeyConst>({
    queryKey: queryKeyConst,
    queryFn: async () => {
      if (!reqID) throw new Error('[useFetchVerificationDetails] reqID is required.');
      try {
        const response = await axiosInstance.request<VerificationPayload>({
          url: ApiConfig.user.verifications_details_pull.endpoint.replace('{reqID}', reqID),
          method: ApiConfig.user.verifications_details_pull.method,
          headers: { 'Content-Type': 'application/json' },
        });
        return response.data;
      } catch (error) {
        throw error;
      }
    },
    enabled: !!reqID,
  });

  useEffect(() => {
    const store = userVerificationDetailsStore.getState();
    if (queryResult.isLoading) {
      store.setIsFetching(true);
    } else if (queryResult.isError && queryResult.error) {
      userVerificationDetailsStore.getState().setError(queryResult.error as any);
      // Token handling is now managed by axios interceptor
    } else if (queryResult.isSuccess && queryResult.data) {
      store.setVerificationDetails(queryResult.data);
      store.setIsFetching(false);
      store.setError(null);
    }
  }, [queryResult.isLoading, queryResult.isError, queryResult.isSuccess, queryResult.data, queryResult.error, reqID, queryClient, requestParams]); // Added requestParams

  return queryResult;
};

// Fetch User's Volunteer Organization Qualifications
export const useFetchUserVolunteerQualifications = (requestParams?: VolunteerOrganizationQualificationsPullRequest, enabled: boolean = true) => {
  const queryClient = useQueryClient();
  const isAuthenticated = authenticationStore((state) => state.isAuthenticated);
  const queryKeyConst = ['userVolunteerQualifications', requestParams] as const;

  const queryResult = useQuery<VolunteerQualificationPayload[], Error, VolunteerQualificationPayload[], typeof queryKeyConst>({
    queryKey: queryKeyConst,
    queryFn: async () => {
      try {
        const response = await axiosInstance.request<VolunteerQualificationPayload[]>({
          url: ApiConfig.user.volunteer_organization_qualifications_pull.endpoint,
          method: ApiConfig.user.volunteer_organization_qualifications_pull.method,
          headers: { 'Content-Type': 'application/json' },
        });
        return response.data;
      } catch (error) {
        throw error;
      }
    },
    enabled: isAuthenticated && enabled, // Only fetch when user is authenticated and enabled
  });

  useEffect(() => {
    const store = userVolunteerQualificationsStore.getState();
    if (queryResult.isLoading) {
      store.setIsFetching(true);
    } else if (queryResult.isError && queryResult.error) {
      store.setError(queryResult.error as any);
      // Token handling is now managed by axios interceptor
    } else if (queryResult.isSuccess && queryResult.data) {
      store.setQualifications(queryResult.data);
      store.setIsFetching(false);
      store.setError(null);
    }
  }, [queryResult.isLoading, queryResult.isError, queryResult.isSuccess, queryResult.data, queryResult.error, requestParams]); // Added requestParams

  return queryResult;
};

// New admin-level hook to fetch volunteer qualifications for a specific user
// This requires admin/volunteer permissions to access
export const useFetchUserVolunteerQualificationsByUserId = (userId: string | undefined, enabled: boolean = true) => {
  const isAuthenticated = authenticationStore((state) => state.isAuthenticated);
  const queryKeyConst = ['userVolunteerQualifications', 'byUserId', userId] as const;

  const queryResult = useQuery<VolunteerQualificationPayload[], Error, VolunteerQualificationPayload[], typeof queryKeyConst>({
    queryKey: queryKeyConst,
    queryFn: async () => {
      try {
        // Use admin endpoint to fetch user volunteer qualifications by user ID
        // This requires volunteer/admin permissions
        const response = await axiosInstance.request<VolunteerQualificationPayload[]>({
          url: ApiConfig.admin?.volunteer_qualifications?.endpoint || `${API_BASE_URL}/admin/volunteer-qualifications`,
          method: ApiConfig.admin?.volunteer_qualifications?.method || 'GET',
          headers: { 'Content-Type': 'application/json' },
          params: {
            user_id: userId,
          },
        });
        return response.data;
      } catch (error) {
        console.error('[useFetchUserVolunteerQualificationsByUserId] Error fetching user volunteer qualifications:', error);
        throw error;
      }
    },
    enabled: isAuthenticated && enabled && !!userId, // Only fetch when user is authenticated, enabled, and userId is provided
  });

  return queryResult;
};

// Account Deletion Types
export interface InitiateAccountDeletionRequest {
  reason?: string;
}

export interface InitiateAccountDeletionResponse {
  message: string;
}

export interface ConfirmAccountDeletionRequest {
  code: string;
}

export interface ConfirmAccountDeletionResponse {
  message: string;
  scheduled_for: string;
}

export interface AccountDeletionStatusResponse {
  status: 'active' | 'pending_confirmation' | 'scheduled' | 'deleted';
  scheduled_for?: string;
  deleted_at?: string;
  recoverable?: boolean;
  expires_at?: string;
}

// Initiate Account Deletion
export const useInitiateAccountDeletion = () => {
  const queryClient = useQueryClient();

  return useMutation<InitiateAccountDeletionResponse, AxiosError, InitiateAccountDeletionRequest>({
    mutationFn: async (data) => {
      const response = await axiosInstance.post<InitiateAccountDeletionResponse>(
        ApiConfig.user.delete_account_initiate.endpoint,
        data,
        {
          headers: { 'Content-Type': 'application/json' },
        }
      );
      return response.data;
    },
    onError: (error) => {
      console.error('[useInitiateAccountDeletion] Error:', error);
    },
  });
};

// Confirm Account Deletion
export const useConfirmAccountDeletion = () => {
  const queryClient = useQueryClient();

  return useMutation<ConfirmAccountDeletionResponse, AxiosError, ConfirmAccountDeletionRequest>({
    mutationFn: async (data) => {
      const response = await axiosInstance.post<ConfirmAccountDeletionResponse>(
        ApiConfig.user.delete_account_confirm.endpoint,
        data,
        {
          headers: { 'Content-Type': 'application/json' },
        }
      );
      return response.data;
    },
    onSuccess: () => {
      // Clear user data from stores as account is now scheduled for deletion
      queryClient.invalidateQueries({ queryKey: ['userProfile'] });
    },
    onError: (error) => {
      console.error('[useConfirmAccountDeletion] Error:', error);
    },
  });
};

// Cancel Scheduled Account Deletion
export const useCancelAccountDeletion = () => {
  const queryClient = useQueryClient();

  return useMutation<{ message: string }, AxiosError>({
    mutationFn: async () => {
      const response = await axiosInstance.post<{ message: string }>(
        ApiConfig.user.delete_account_cancel.endpoint,
        {},
        {
          headers: { 'Content-Type': 'application/json' },
        }
      );
      return response.data;
    },
    onSuccess: () => {
      // Refresh deletion status
      queryClient.invalidateQueries({ queryKey: ['accountDeletionStatus'] });
    },
    onError: (error) => {
      console.error('[useCancelAccountDeletion] Error:', error);
    },
  });
};

// Get Account Deletion Status
export const useFetchAccountDeletionStatus = (enabled: boolean = true) => {
  const queryClient = useQueryClient();
  const isAuthenticated = authenticationStore((state) => state.isAuthenticated);

  return useQuery<AccountDeletionStatusResponse, Error>({
    queryKey: ['accountDeletionStatus'],
    queryFn: async () => {
      const response = await axiosInstance.get<AccountDeletionStatusResponse>(
        ApiConfig.user.delete_account_status.endpoint,
        {
          headers: { 'Content-Type': 'application/json' },
        }
      );
      return response.data;
    },
    enabled: isAuthenticated && enabled,
    staleTime: 30 * 1000, // 30 seconds
  });
};