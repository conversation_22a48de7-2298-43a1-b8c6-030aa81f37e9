import {
  useQuery,
  useMutation,
  useQueryClient,
  useInfiniteQuery
} from '@tanstack/react-query';
import axiosInstance from './axios_instance';
import { AxiosError } from 'axios';
import { authenticationStore } from '@/stores/authentication_store';
import {
  fetchIntervalMs,
  ApiConfig,
  type EventRegisterRequest,
  type RegisteredEventsListPullRequest,
  type RegisteredEventsDetailsPullRequest,
  type RegisteredEventsCancelRequest,
  type EventRegistrationPayload,
} from '@/api/api_config';
import {
  registeredEventsListStore,
  registeredEventDetailsStore,
} from 'stores/user_events_store'; // Assuming you will create this
import { useEffect } from 'react';



// Register for an Event
export const useRegisterForEvent = () => {
  const queryClient = useQueryClient();
  return useMutation<EventRegistrationPayload, Error, EventRegisterRequest>({
    mutationFn: async (payload: EventRegisterRequest): Promise<EventRegistrationPayload> => {
      try {
        const response = await axiosInstance.request<EventRegistrationPayload>({
          url: ApiConfig.user_events.event_register.endpoint,
          method: ApiConfig.user_events.event_register.method,
          data: payload,
          headers: { 'Content-Type': 'application/json' },
        });
        return response.data;
      } catch (error) {
        throw error;
      }
    },
    onSuccess: (data: EventRegistrationPayload, variables) => {
      console.log(`[user_events_services] useRegisterForEvent successful, response: ${JSON.stringify(data)}`);
      queryClient.invalidateQueries({ queryKey: ['registeredEventsList'] });
      queryClient.invalidateQueries({ queryKey: ['eventDetails', variables.event_id] }); // Also invalidate public event details
      // Potentially update a specific event detail store if it holds registration status
    },
    onError: (error: Error, variables: EventRegisterRequest) => {
      console.error(`[user_events_services] useRegisterForEvent error: ${error}, request: ${JSON.stringify(variables)}`);
      // Token handling is now managed by axios interceptor
    },
  });
};

// Fetch Registered Events List
export const useFetchRegisteredEventsList = (requestParams?: RegisteredEventsListPullRequest) => {
  const queryClient = useQueryClient();
  const queryKeyConst = ['registeredEventsList', requestParams] as const;
  
  // Check if user is authenticated before making request
  const isAuthenticated = authenticationStore(state => state.isAuthenticated);

  const queryResult = useQuery<EventRegistrationPayload[], Error, EventRegistrationPayload[], typeof queryKeyConst>({
    queryKey: queryKeyConst,
    queryFn: async () => {
      try {
        const response = await axiosInstance.request<EventRegistrationPayload[]>({
          url: ApiConfig.user_events.registered_events_list_pull.endpoint,
          method: ApiConfig.user_events.registered_events_list_pull.method,
          params: requestParams,
          headers: { 'Content-Type': 'application/json' },
        });
        return response.data;
      } catch (error) {
        throw error;
      }
    },
    enabled: isAuthenticated, // Only fetch when user is authenticated
    // onError: (error: Error) => { // Deprecated
    //     registeredEventsListStore.getState().setError(error as any);
    //     handleApiError(error, queryClient, 'user_events_services (useFetchRegisteredEventsList)', requestParams, queryKeyConst);
    // }
  });

  useEffect(() => {
    const store = registeredEventsListStore.getState();
    if (queryResult.isLoading) {
      store.setIsFetching(true);
    } else if (queryResult.isError && queryResult.error) {
      registeredEventsListStore.getState().setError(queryResult.error as any);
      // Token handling is now managed by axios interceptor
    } else if (queryResult.isSuccess && queryResult.data) {
      store.setRegisteredEventsList(queryResult.data);
      store.setIsFetching(false);
      store.setError(null);
    }
  }, [queryResult.isLoading, queryResult.isError, queryResult.isSuccess, queryResult.data, queryResult.error, requestParams, queryClient]);

  return queryResult;
};

// Fetch Registered Event Details
export const useFetchRegisteredEventDetails = (requestParams: RegisteredEventsDetailsPullRequest) => {
  const queryClient = useQueryClient();
  const { registrationId } = requestParams;
  const queryKeyConst = ['registeredEventDetails', registrationId] as const;

  // Check if user is authenticated before making request
  const isAuthenticated = authenticationStore(state => state.isAuthenticated);

  const queryResult = useQuery<EventRegistrationPayload, Error, EventRegistrationPayload, typeof queryKeyConst>(
    {
      queryKey: queryKeyConst,
      queryFn: async () => {
        if (!registrationId) throw new Error('[useFetchRegisteredEventDetails] registrationId is required.');
        try {
          const response = await axiosInstance.request<EventRegistrationPayload>({
            url: ApiConfig.user_events.registered_events_details_pull.endpoint.replace('{registrationId}', registrationId),
            method: ApiConfig.user_events.registered_events_details_pull.method,
            headers: { 'Content-Type': 'application/json' },
          });
          return response.data;
        } catch (error) {
          throw error;
        }
      },
      enabled: !!registrationId && isAuthenticated, // Only fetch when registrationId exists and user is authenticated
    }
  );

  useEffect(() => {
    const store = registeredEventDetailsStore.getState();
    if (queryResult.isLoading) {
      store.setIsFetching(true);
    } else if (queryResult.isError && queryResult.error) {
      store.setError(queryResult.error as any);
      // Token handling is now managed by axios interceptor
    } else if (queryResult.isSuccess && queryResult.data) {
      store.setRegisteredEventDetails(queryResult.data);
      store.setIsFetching(false);
      store.setError(null);
      console.log(`[user_events_services] registeredEventDetailsStore setRegisteredEventDetails successful`);
    }
  }, [queryResult.isLoading, queryResult.isError, queryResult.isSuccess, queryResult.data, queryResult.error, registrationId, requestParams, queryClient]);

  return queryResult;
};

// Cancel Event Registration
export const useCancelEventRegistration = () => {
  const queryClient = useQueryClient();
  return useMutation<EventRegistrationPayload, Error, RegisteredEventsCancelRequest & { eventId?: string }>({
    mutationFn: async (payload: RegisteredEventsCancelRequest & { eventId?: string }): Promise<EventRegistrationPayload> => {
      const { registrationId, ...rest } = payload;
      try {
        const response = await axiosInstance.request<EventRegistrationPayload>({
          url: ApiConfig.user_events.registered_events_cancel.endpoint.replace('{registrationId}', registrationId),
          method: ApiConfig.user_events.registered_events_cancel.method,
          // data: rest, // PATCH might have a body, though cancel often doesn't need one
          headers: { 'Content-Type': 'application/json' },
        });
        return response.data;
      } catch (error) {
        throw error;
      }
    },
    onSuccess: (data: EventRegistrationPayload, variables) => {
      console.log(`[user_events_services] useCancelEventRegistration successful, response: ${JSON.stringify(data)}`);
      queryClient.invalidateQueries({ queryKey: ['registeredEventsList'] });
      queryClient.invalidateQueries({ queryKey: ['registeredEventDetails', variables.registrationId] });
      if (variables.eventId) {
        queryClient.invalidateQueries({ queryKey: ['eventDetails', variables.eventId] }); // Also invalidate public event details
      }
    },
    onError: (error: Error, variables: RegisteredEventsCancelRequest & { eventId?: string }) => {
      console.error(`[user_events_services] useCancelEventRegistration error: ${error}, request: ${JSON.stringify(variables)}`);
      // Token handling is now managed by axios interceptor
    },
  });
};
