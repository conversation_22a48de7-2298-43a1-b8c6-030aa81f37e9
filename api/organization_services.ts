import {
  useQuery,
  useMutation,
  useQueryClient,
} from '@tanstack/react-query';
import axiosInstance from './axios_instance';
import { AxiosError } from 'axios';
import { authenticationStore } from '@/stores/authentication_store';
import {
  fetchIntervalMs,
  ApiConfig,
  type OrganizationListPullRequest,
  type OrganizationDetailsPullRequest,
  type OrganizationJoinRequest,
  type OrganizationLeaveRequest,
  type OrganizationListPayload,
  type OrganizationJoinResponse,
  type OrganizationLeaveResponse,
} from '@/api/api_config';
import {
  organizationListStore,
  organizationDetailsStore,
  // You might create stores for join/leave mutations if needed for optimistic updates or specific state management
} from 'stores/organization_store'; // Assuming you will create this
import { useEffect } from 'react';



// Fetch Organization List
export const useFetchOrganizationList = (requestParams?: OrganizationListPullRequest) => {
  const queryClient = useQueryClient();
  const queryKeyConst = ['organizationList', requestParams] as const;
  
  const queryResult = useQuery<OrganizationListPayload[], Error, OrganizationListPayload[]>({
    queryKey: queryKeyConst,
    queryFn: async () => {
      try {
        const response = await axiosInstance.request<OrganizationListPayload[]>({
          url: ApiConfig.organization.organization_list_pull.endpoint,
          method: ApiConfig.organization.organization_list_pull.method,
          headers: { 'Content-Type': 'application/json' },
        });
        return response.data;
      } catch (error) {
        throw error;
      }
    },
    // onError: (error: Error) => { // Deprecated
    //     organizationListStore.getState().setError(error as any);
    //     handleApiError(error, queryClient, 'organization_services (useFetchOrganizationList)', requestParams, queryKeyConst);
    // }
  });

  useEffect(() => {
    const store = organizationListStore.getState();
    if (queryResult.isLoading) {
      store.setIsFetching(true);
    } else if (queryResult.isError && queryResult.error) {
      store.setError(queryResult.error as any);
      // Token handling is now managed by axios interceptor
    } else if (queryResult.isSuccess && queryResult.data) {
      store.setOrganizationList(queryResult.data);
      store.setIsFetching(false);
      store.setError(null); // Use null to clear error as per store type
    }
  }, [queryResult.isLoading, queryResult.isError, queryResult.isSuccess, queryResult.data, queryResult.error, requestParams]);

  return queryResult;
};

// Fetch Organization Details
export const useFetchOrganizationDetails = (requestParams: OrganizationDetailsPullRequest) => {
  const queryClient = useQueryClient();
  const { orgId } = requestParams;
  const queryKeyConst = ['organizationDetails', orgId] as const;

  const queryResult = useQuery<OrganizationListPayload, Error, OrganizationListPayload>({
    queryKey: queryKeyConst,
    queryFn: async () => {
      if (!orgId) throw new Error('[useFetchOrganizationDetails] orgId is required.');
      try {
        const response = await axiosInstance.request<OrganizationListPayload>({
          url: ApiConfig.organization.organization_details_pull.endpoint.replace('{orgId}', orgId),
          method: ApiConfig.organization.organization_details_pull.method,
          headers: { 'Content-Type': 'application/json' },
        });
        return response.data;
      } catch (error) {
        throw error;
      }
    },
    enabled: !!orgId,
    // onError: (error: Error) => { // Deprecated
    //     organizationDetailsStore.getState().setError(error as any);
    //     handleApiError(error, queryClient, 'organization_services (useFetchOrganizationDetails)', requestParams, queryKeyConst);
    // }
  });

  useEffect(() => {
    const store = organizationDetailsStore.getState();
    if (queryResult.isLoading) {
      store.setIsFetching(true);
    } else if (queryResult.isError && queryResult.error) {
      store.setError(queryResult.error as any);
      // Token handling is now managed by axios interceptor
    } else if (queryResult.isSuccess && queryResult.data) {
      store.setOrganizationDetails(queryResult.data);
      store.setIsFetching(false);
      store.setError(null); // Use null to clear error as per store type
    }
  }, [queryResult.isLoading, queryResult.isError, queryResult.isSuccess, queryResult.data, queryResult.error, orgId, requestParams]);

  return queryResult;
};

// Join Organization
export const useJoinOrganization = () => {
  const queryClient = useQueryClient();
  return useMutation<OrganizationJoinResponse, Error, OrganizationJoinRequest & { orgId: string }>({
    mutationFn: async (payload: OrganizationJoinRequest & { orgId: string }): Promise<OrganizationJoinResponse> => {
      const { orgId, ...rest } = payload;
      try {
        const response = await axiosInstance.request<OrganizationJoinResponse>({
          url: ApiConfig.organization.organization_join.endpoint.replace('{orgId}', orgId),
          method: ApiConfig.organization.organization_join.method,
          data: rest,
          headers: { 'Content-Type': 'application/json' },
        });
        return response.data;
      } catch (error) {
        throw error;
      }
    },
    onSuccess: (data: OrganizationJoinResponse, variables) => {
      // console.log(`[organization_services] useJoinOrganization successful, response: ${JSON.stringify(data)}`);
      queryClient.invalidateQueries({ queryKey: ['organizationList'] });
      queryClient.invalidateQueries({ queryKey: ['organizationDetails', variables.orgId] });
    },
    onError: (error: Error, variables: OrganizationJoinRequest & { orgId: string }) => {
      console.error(`[organization_services] useJoinOrganization error: ${error}, request: ${JSON.stringify(variables)}`);
      // Token handling is now managed by axios interceptor
    }
  });
};

// Leave Organization
export const useLeaveOrganization = () => {
  const queryClient = useQueryClient();
  return useMutation<OrganizationLeaveResponse, Error, OrganizationLeaveRequest & { orgId: string }>({
    mutationFn: async (payload: OrganizationLeaveRequest & { orgId: string }): Promise<OrganizationLeaveResponse> => {
      const { orgId, ...rest } = payload;
      try {
        const response = await axiosInstance.request<OrganizationLeaveResponse>({
          url: ApiConfig.organization.organization_leave.endpoint.replace('{orgId}', orgId),
          method: ApiConfig.organization.organization_leave.method,
          data: rest,
          headers: { 'Content-Type': 'application/json' },
        });
        return response.data;
      } catch (error) {
        throw error;
      }
    },
    onSuccess: (data: OrganizationLeaveResponse, variables) => {
      // console.log(`[organization_services] useLeaveOrganization successful, response: ${JSON.stringify(data)}`);
      queryClient.invalidateQueries({ queryKey: ['organizationList'] });
      queryClient.invalidateQueries({ queryKey: ['organizationDetails', variables.orgId] });
    },
    onError: (error: Error, variables: OrganizationLeaveRequest & { orgId: string }) => {
      console.error(`[organization_services] useLeaveOrganization error: ${error}, request: ${JSON.stringify(variables)}`);
      // Token handling is now managed by axios interceptor
    }
  });
};
