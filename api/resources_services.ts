import {
  useQuery,
  useInfiniteQuery,
  // useMutation, // No mutations for resources in ApiConfig, download is a GET
  useQueryClient,
} from '@tanstack/react-query';
import axiosInstance from './axios_instance';
import { AxiosError } from 'axios';
import { authenticationStore } from '@/stores/authentication_store';
import {
  fetchIntervalMs,
  ApiConfig,
  type ResourceListPullRequest,
  type ResourceDetailsPullRequest,
  type ResourceDownloadRequest, // While this is a GET, it might be handled differently (e.g., opening a link)
  type ResourceListPayload,
  type ResourceDownloadResponse,
} from '@/api/api_config';
import {
  resourceListStore,
  resourceDetailsStore,
  // resourceDownloadStore, // Unlikely to be a store, download usually triggers browser behavior
} from 'stores/resources_store'; // Assuming you will create this
import { useEffect } from 'react';



// Fetch Resource List
export const useFetchResourceList = (requestParams?: ResourceListPullRequest) => {
  const queryClient = useQueryClient();
  const queryKeyConst = ['resourceList', requestParams] as const;
  const queryResult = useQuery<ResourceListPayload[], Error, ResourceListPayload[], typeof queryKeyConst>(
    {
      queryKey: queryKeyConst,
      queryFn: async () => {
        try {
          const response = await axiosInstance.request<ResourceListPayload[]>({
            url: ApiConfig.resources.resource_list_pull.endpoint,
            method: ApiConfig.resources.resource_list_pull.method,
            params: requestParams,
            headers: { 'Content-Type': 'application/json' },
          });
          return response.data;
        } catch (error) {
          throw error;
        }
      },
    }
  );

  useEffect(() => {
    const store = resourceListStore.getState();
    if (queryResult.isLoading) {
      store.setIsFetching(true);
    } else if (queryResult.isError && queryResult.error) {
      store.setError(queryResult.error as any);
      // Token handling is now managed by axios interceptor
    } else if (queryResult.isSuccess && queryResult.data) {
      store.setResourceList(queryResult.data);
      store.setIsFetching(false);
    }
  }, [queryResult.isLoading, queryResult.isError, queryResult.isSuccess, queryResult.data, queryResult.error, requestParams]);

  return queryResult;
};

// Fetch Resource Details
export const useFetchResourceDetails = (requestParams: ResourceDetailsPullRequest) => {
  const queryClient = useQueryClient();
  const { resourceId } = requestParams;
  const queryKeyConst = ['resourceDetails', resourceId] as const;

  const queryResult = useQuery<ResourceListPayload, Error, ResourceListPayload, typeof queryKeyConst>(
    {
      queryKey: queryKeyConst,
      queryFn: async () => {
        if (!resourceId) throw new Error('[useFetchResourceDetails] resourceId is required.');
        try {
          const response = await axiosInstance.request<ResourceListPayload>({
            url: ApiConfig.resources.resource_details_pull.endpoint.replace('{resourceId}', resourceId),
            method: ApiConfig.resources.resource_details_pull.method,
            headers: { 'Content-Type': 'application/json' },
          });
          return response.data;
        } catch (error) {
          throw error;
        }
      },
      enabled: !!resourceId,
    }
  );

  useEffect(() => {
    const store = resourceDetailsStore.getState();
    if (queryResult.isLoading) {
      store.setIsFetching(true);
    } else if (queryResult.isError && queryResult.error) {
      store.setError(queryResult.error as any);
      // Token handling is now managed by axios interceptor
    } else if (queryResult.isSuccess && queryResult.data) {
      store.setResourceDetails(queryResult.data);
      store.setIsFetching(false);
      store.setError(null);
    }
  }, [queryResult.isLoading, queryResult.isError, queryResult.isSuccess, queryResult.data, queryResult.error, resourceId, requestParams, queryClient]);

  return queryResult;
};

// Handle Resource Download (this might not use react-query's useQuery directly if it's just a link)
// This function could construct the URL and trigger a download or navigation.
export const getResourceDownloadUrl = (requestParams: ResourceDownloadRequest): string => {
  const { orgId, resourceIdOrSlug, fileIdOrName } = requestParams;
  if (!orgId || !resourceIdOrSlug || !fileIdOrName) {
    console.error('[resources_services] Missing parameters for getResourceDownloadUrl', requestParams);
    throw new Error('orgId, resourceIdOrSlug, and fileIdOrName are required for resource download.');
  }
  let url = ApiConfig.resources.resource_download.endpoint;
  url = url.replace('{orgId}', orgId);
  url = url.replace('{resourceIdOrSlug}', resourceIdOrSlug);
  url = url.replace('{fileIdOrName}', fileIdOrName);
  return url;
};

// If you need to fetch data about the download (e.g., a pre-signed URL) before actually downloading,
// then a useQuery/useMutation hook might be appropriate.
// For a direct download, the above utility function is more common.
// Example: using a simple function to trigger download, not a react-query hook
export const triggerResourceDownload = async (requestParams: ResourceDownloadRequest) => {
    const url = getResourceDownloadUrl(requestParams);
    // This part depends on how you want to handle the download in a React Native context
    // For web, it might be window.open(url, '_blank');
    // For React Native, you might use Linking.openURL(url) or a file download library.
    console.log(`[resources_services] Triggering download from URL: ${url}`);
    // Placeholder for actual download logic, e.g.:
    // import { Linking } from 'react-native';
    // Linking.openURL(url).catch(err => console.error("Couldn't load page", err));
    try {
        // This is a GET request, so axios.get or axios.request with method GET
        // The response is expected to be the file itself or a redirect to it.
        // Handling binary file downloads with axios requires responseType: 'blob' or 'arraybuffer'
        // and then further processing to save the file, which is complex and platform-specific.
        // For simplicity, we'll assume the endpoint directly serves the file and browser/OS handles it.
        // If the API returns JSON with a download link, that would be handled differently.

        // This example won't actually download a file but shows where you'd integrate it.
        // The `ResourceDownloadResponse` expects a `file: string` which might be a base64 string or URL.
        // If it returns a URL, then `Linking.openURL` is appropriate.
        // If it returns file content, you need a file system API to save it.
        alert(`Download would be initiated for: ${url}. Actual download implementation needed.`);
        // Simulating a response for demonstration purposes if needed
        // return { file: "simulated_file_content_or_url" } as ResourceDownloadResponse;
    } catch (error) {
        console.error(`[resources_services] Error during resource download trigger for ${url}:`, error);
        throw error;
    }
};
