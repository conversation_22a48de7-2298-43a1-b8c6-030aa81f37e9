import { QueryClient } from '@tanstack/react-query'
import { env } from '@/config/environment';

// ------------------------------------------------- Global Config
export const API_BASE_URL = process.env.EXPO_PUBLIC_API_BASE || "https://the-moment-api.haytech.io/api/v1";
export const MEDIA_BASE_URL = process.env.EXPO_PUBLIC_MEDIA_BASE || "https://the-moment-api.haytech.io";
export const queryClient = new QueryClient({
  defaultOptions: {
    queries: {
      refetchInterval: 60000,
      retry: 3,
      staleTime: 5 * 60 * 1000, // 5 minutes in milliseconds
    },
  },
});
export const fetchIntervalMs = 60000;

// ------------------------------------------------- Global Payloads
export interface LanguageSettingPayload {
    interface_language?: string;
    communication_language?: string;
}

export interface NotificationSettingPayload {
    enable_app_notifications?: boolean;
    enable_whatsapp_notifications?: boolean;
}

export interface VerificationStatusPayload {
    hk_id_card: boolean;
    mainland_china_id_card: boolean;
    mainland_travel_permit: boolean;
    passport: boolean;
    hk_youth_plus: boolean;
    address_proof: boolean;
    student_id: boolean;
    home_visit: boolean;
}

export interface MonthlyAttendedEventPayload {
	month: string;
	count: number;
}

export interface TopAttendedEventPayload {
	name: string;
	count: number;
}

export interface MediaItemPayload {
    id: string;
    file_name: string;
    file_path: string;
    file_type: string;
    file_size: number;
    uploaded_at: string;
    is_banner: boolean;
}

export interface PostTagPayload {
    id: string;
    name_en: string;
    name_zh_hk: string;
    name_zh_cn: string;
}

export interface ResourceFilePayload {
    id: string;
	resource_id: string;
	file_name: string;
	file_path: string; // Public URL
	file_type: string;
	file_size: number;
	uploaded_at: string;
	description?: string;
}

export interface OrgSlimPayload {
    id: string;
	name: string;
}

export interface TypesPayload {
    key: string;
    name: string;
    lang_code: string;
}

// --------------------------------------------------Request Payloads
// ------------------------------------------------- Authentications
export interface PhoneCheckRequest {
	phone: string;
}

export interface ExistingPhoneOtpInitiateRequest {
    phone: string;
    client_id: string;
    code_challenge: string;
    code_challenge_method: 'S256';
    state: string;
    phone_otp_channel: 'whatsapp' | 'sms';
    redirect_uri?: string;
}

export interface ExistingPhoneOtpVerifyRequest {
    state: string;
    otp: string;
    code_verifier: string;
}
  
export interface NewPhoneOtpInitiateRequest {
    phone: string;
    client_id: string;
    code_challenge: string;
    code_challenge_method: 'S256';
    state: string;
    phone_otp_channel: 'whatsapp';
    redirect_uri?: string;
}

export interface NewPhoneOtpVerifyRequest {
    state: string;
    otp: string;
    code_verifier: string;
    display_name: string;
    interface_language?: string;
    communication_language?: string;
    phone_otp_channel: 'whatsapp';
}

export interface AccessTokenRefreshRequest {
	refresh_token: string;
}

export interface LogoutRequest {
    refresh_token: string;
}

// ------------------------------------------------- Users
export interface ProfilePullRequest {
}

export interface ProfilePushRequest {
    display_name?: string;
    language_preferences?: LanguageSettingPayload;
    notification_settings?: NotificationSettingPayload;
    profile_picture_url?: string;
}

export interface ProfileUploadIconRequest {
    file: string;
}

export interface ChangePhoneOtpInitiateRequest {
    new_phone_number: string;
    phone_otp_channel: 'whatsapp' | 'sms';
    client_id: string;
    state: string;
}

export interface ChangePhoneOtpVerifyRequest {
    state: string;
    otp: string;
    new_phone_number: string;
}

export interface OrganizationListPullRequest {
}

export interface StatisticsPullRequest {
}

export interface UserIdPullRequest {
}

export interface VerificationsPullRequest {
}

export interface VerificationsPushRequest {
    formData: FormData;
}

export interface VerificationsDetailsPullRequest {
    reqID: string;
}

export interface VolunteerOrganizationQualificationsPullRequest {
}

// ------------------------------------------------- Organizations
export interface OrganizationDetailsPullRequest {
    orgId: string;
}

export interface OrganizationJoinRequest {
    orgId: string;
}

export interface OrganizationLeaveRequest {
    orgId: string;
}

// ------------------------------------------------- Public Events
export interface PublishedEventListPullRequest {
    limit?: number;
    offset?: number;
    status?: string;
    start_date?: string;
    end_date?: string;
    search_term?: string;
    government_funding_keys?: string[];
    organization_id?: string;
    organization_id2?: string;
    tagIds?: string[];
    event_verification_type_key?: string;
}

export interface EventDetailsPullRequest {
    eventId: string;
}

export interface EventTagsPullRequest {
}

export interface EventCheckInRequest {
    user_id: string;
}

// ------------------------------------------------- User Events
export interface EventRegisterRequest {
    event_id: string;
}

export interface RegisteredEventsListPullRequest {
    limit?: number;
    offset?: number;
    start_date?: string;
    end_date?: string;
    status?: string;
    sort?: string;
    role?: 'participant' | 'volunteer';
    organization_id?: string;
    event_id?: string;
}

export interface RegisteredEventsDetailsPullRequest {
    registrationId: string;
}

export interface RegisteredEventsCancelRequest {
    registrationId: string;
}

// ------------------------------------------------- Posts
export interface PublishedPostsListPullRequest {
	organization_id?: string;
	organization_id2?: string;
	tagIds?: string[];
	limit?: number;
	offset?: number;
	status?: string;
	start_date?: string;
	end_date?: string;
	search_term?: string;
}

export interface PostDetailsPullRequest {
    postId: string;
}

export interface PostTagsPullRequest {
}  

// ------------------------------------------------- Resources
export interface ResourceListPullRequest {
    organization_id?: string;
	organization_id2?: string;
	visibility?: string;
	limit?: number;
	offset?: number;
	status?: string;
	search_term?: string;
}

export interface ResourceDetailsPullRequest {
    resourceId: string;
}

export interface ResourceDownloadRequest {
    orgId: string;
    resourceIdOrSlug: string;
    fileIdOrName: string;
}

// ------------------------------------------------- Volunteer
export interface VolunteerOrganizationQualificationsApplyRequest {
    orgId: string;
}

export interface VolunteerOrganizationQualificationsWithdrawRequest {
    appId: string;
}

export interface EventVolunteerApplyRequest {
    eventId: string;
    motivation?: string;
}

export interface EventVolunteerWithdrawRequest {
    appId: string;
}

export interface UserEventVolunteerApplicationsListPullRequest {
    limit: number;
    offset: number;
    user_id?: string;
    event_id?: string;
}

export interface UserEventVolunteerApplicationDetailsPullRequest {
    appId: string;
}

export interface VolunteerGetVerificationsRequest {
    user_id: string;
}

// ------------------------------------------------- Utils
export interface VerificationTypesPullRequest {
    lang_code: string;
}

export interface GovernmentFundingTypesPullRequest {
    lang_code: string;
}

// ------------------------------------------------- Response Payloads
// ------------------------------------------------- Authentications
export interface PhoneCheckResponse {
    exists: boolean;
    user?: any;
}

export interface PhoneOtpInitiateResponse {
    state: string;
    message?: string;
    flow_id?: string;
}

export interface PhoneOtpVerifyResponse {
    message: string;
    user_id: string;
    access_token: string;
    refresh_token: string;
}

export interface AccessTokenRefreshResponse {
	access_token: string;
	refresh_token: string;
	token_type: string;
}

export interface LogoutResponse {
}

// ------------------------------------------------- Users
export interface ProfileResponse {
    id: string;
    display_name: string;
    phone: string;
    phone_verified_at: string;
    phone_otp_channel: string;
    interface_language: string;
    communication_language: string;
    enable_app_notifications: boolean;
    enable_whatsapp_notifications: boolean;
    is_staff: boolean;
    verification_status: VerificationStatusPayload | null;
    created_at: string;
    updated_at: string;
    email?: string;
    email_verified_at?: string;
    profile_picture_url?: string;
}

export interface ChangePhoneOtpVerifyResponse {
    user: ProfileResponse;
    message?: string;
}

export interface StatisticsPullResponse {
    totalEvents: number;
    userJoinedAt: string;
    volunteerEvents: number;
    monthlyAttendedEvents: MonthlyAttendedEventPayload[] | [];
    topAttendedEventTags: TopAttendedEventPayload[] | [];
}

export interface UserIdPullResponse {
    uuid: string;
}

export interface VerificationPayload {
    id: string;
	user_id: string;
	verification_type: string;
	status: 'pending' | 'approved' | 'rejected';
	document_id: string;
	file_name: string;
	mime_type: string;
	document_id_2: string;
	file_name_2: string;
	mime_type_2: string;
	submitted_at: string;
	reviewed_at: string;
	reviewed_by_user_id: string;
	admin_notes: string;
	created_at: string;
	updated_at: string;
	user_display_name: string;
	user_email: string;
	reviewer_display_name: string;
	specifics: any;
}

// ------------------------------------------------- Organizations
export interface OrganizationListPayload {
	id: string | null;
	name: string;
	description: string;
	owner_user_id: string;
	is_default_org: boolean;
	image_url?: string;
	theme_color?: string;
	status: string;
	created_at: string;
	updated_at: string;
}

export interface OrganizationJoinResponse {
    user_id: string;
    org_id: string;
    role: string;
    is_active: boolean;
    notifications_enabled: boolean;
}

export interface OrganizationLeaveResponse {
}

// ------------------------------------------------- Public Events
export interface EventListPayload {
    id: string;
    organization_id: string;
    organization_name: string;
    title: string;
    jsonContent: any;
    location_type: string;
    location_full_address: string;
    location_online_url: string;
    start_time: string;
    end_time: string;
    price: string;
    government_funding_keys: string[];
    status: string;
    participant_limit: number;
    banner_image_urls?: string[];
    media_items: MediaItemPayload[];
    tags: EventTagPayload[];
    verification_type_keys: string[];
    published_at: string;
    registered_count: number;
    waitlisted_count: number;
    attended_count: number;
    waitlist_limit: number;
}

export interface EventListPayloadDetails {
    attended_count: number;
    contact_email: string;
    contact_phone: string;
    created_at: string;
    created_by_user_id: string;
    current_user_registration_id: string;
    current_user_registration_status: string;
    current_user_volunteer_application_id: string;
    current_user_volunteer_status: string;
    end_time: string;
    event_verification_type_key: string;
    government_funding_keys: string[];
    id: string;
    jsonContent: string[];
    location_full_address: string;
    location_online_url: string;
    location_type: string;
    media_items: MediaItemPayload[];
    organization_id: string;
    organization_name: string;
    participant_limit: number;
    price: string;
    published_at: string;
    registered_count: number;
    verification_type_keys: string[];
    requires_approval_for_registration: boolean;
    start_time: string;
    status: string;
    tags: EventTagPayload[];
    title: string;
    updated_at: string;
    waitlist_limit: number;
    waitlisted_count: number;
}

export interface EventTagPayload {
    id: string;
    name_en: string;
    name_zh_hk: string;
    name_zh_cn: string;
}

// ------------------------------------------------- User Events
export interface EventRegistrationPayload {
    id: string;
	event_id: string;
	user_id: string;
	status: string;
	payment_status: string;
	registration_role: string;
	registered_at: string;
	attended_at: string;
	cancellation_reason_by_user: string;
	admin_notes_on_registration: string;
	waitlist_priority: string;
	created_at: string;
	updated_at: string;
	check_in_by_user_id: string;
	check_in_method: string;
    event_title: string;
    event_start_time: string;
    event_end_time: string;
    event_description: string;
    event_location_type: string;
    event_location_full_address: string;
    event_location_online_url: string;
    event_status: string;
    event_organization_id: string;
    event_organization_name: string;
    event_price: string;
    event_contact_email: string;
    event_contact_phone: string;
    registered_count: number;
    waitlisted_count: number;
    media_items: MediaItemPayload[];
    user_display_name: string;
    user_email: string;
    user_phone: string; 
}

// ------------------------------------------------- Posts
export interface PostListPayload {
    id: string;
	organization_id: string;
	author_display_name: string;
	title: string;
	slug: string;
    content: any;
	status: string;
	published_at: string;
	created_at: string;
	updated_at: string;
	media_items: MediaItemPayload[];
	tags: PostTagPayload[];
}

export interface PostTagsPullResponse {
    tags: PostTagPayload[] | [];
}

// ------------------------------------------------- Resources
export interface ResourceListPayload {
    id: string;
	organization_id: string;
	title: string;
	slug: string;
	description: string;
	visibility: string;
	status: string;
	published_at: string;
	created_at: string;
	updated_at: string;
	files: ResourceFilePayload[];
	organization: OrgSlimPayload; 
}

export interface ResourceDownloadResponse {
    file: string;
}

// ------------------------------------------------- Volunteer
export interface VolunteerOrganizationQualificationsApplyResponse {
    message: string;
    application_id: string;
}

export interface VolunteerQualificationPayload {
    id: string;
    user_id: string; // Kept for consistency, though often redundant if it's /me/
    organization_id: string;
    organization_name: string;
    application_date: string;
    status: string;
    motivation?: string;
    reviewed_by_user_id?: string;
    reviewer_display_name?: string; // For admin views
    review_date?: string;
    admin_notes?: string;
    created_at: string;
    updated_at: string;
    applicant_display_name?: string;
    applicant_email?: string;
    applicant_phone?: string;
}

export interface EventVolunteerApplyResponse {
    id: string;
    event_id: string;
    user_id: string;
    organization_id: string;
    applied_at: string;
    created_at: string;
    updated_at: string;
    status: string;
    application_notes_by_user?: string;
    admin_review_notes?: string;
    reviewed_at?: string;
    reviewed_by_user_id?: string;
    attended_at?: string;
}

export interface EventVolunteerApplicationPayload {
    id: string;
	event_id: string;
	user_id: string;
	organization_id: string;
	application_notes_by_user: string;
	admin_review_notes: string;
	applied_at: string;
	reviewed_at: string;
	reviewed_by_user_id: string;
	created_at: string;
	updated_at: string;
	status: string;
	attended_at: string;
	event_title: string;
	event_start_time: string;
	event_end_time: string;
	event_description: string;
	event_location_type: string;
	event_location_full_address: string;
	event_location_online_url: string;
	event_status: string;
	event_organization_id: string;
	organization_name: string;
	event_price: string;
	event_contact_email: string;
	event_contact_phone: string;
	media_items: MediaItemPayload[];
	user_display_name: string;
	user_email: string;
	user_phone: string;
	reviewer_display_name: string;
}

export interface EventVolunteerApplicationPayload {
    event_volunteer_application: EventVolunteerApplicationPayload | null;
}

export interface EventVolunteerApplicationPayload {
    event_volunteer_applications: EventVolunteerApplicationPayload[] | [];
}

export interface EventVolunteerApplicationPayload {
    event_volunteer_application: EventVolunteerApplicationPayload | null;
}

// ------------------------------------------------- Utils
export interface TypesPullResponse {
    types: TypesPayload[];
}

// ------------------------------------------------- API Config
export const ApiConfig = {
    authentication: {
        phone_check: {
            endpoint: `${API_BASE_URL}/authn/phone/check`,
            method: 'POST',
            request_payload: {} as PhoneCheckRequest,
            response_payload: {} as PhoneCheckResponse,
        },
        existing_phone_otp_initiate: {
            endpoint: `${API_BASE_URL}/authn/phone/otp/initiate`,
            method: 'POST',
            request_payload: {} as ExistingPhoneOtpInitiateRequest,
            response_payload: {} as PhoneOtpInitiateResponse,
        },
        existing_phone_otp_verify: {
            endpoint: `${API_BASE_URL}/authn/phone/otp/verify`,
            method: 'POST',
            request_payload: {} as ExistingPhoneOtpVerifyRequest,
            response_payload: {} as PhoneOtpVerifyResponse,
        },
        new_phone_otp_initiate: {
            endpoint: `${API_BASE_URL}/authn/register/phone/initiate`,
            method: 'POST',
            request_payload: {} as NewPhoneOtpInitiateRequest,
            response_payload: {} as PhoneOtpInitiateResponse,
        },
        new_phone_otp_verify: {
            endpoint: `${API_BASE_URL}/authn/register/phone/verify`,
            method: 'POST',
            request_payload: {} as NewPhoneOtpVerifyRequest,
            response_payload: {} as PhoneOtpVerifyResponse,
        },
        access_token_refresh: {
            endpoint: `${API_BASE_URL}/authn/token/refresh`,
            method: 'POST',
            request_payload: {} as AccessTokenRefreshRequest,
            response_payload: {} as AccessTokenRefreshResponse,
        },
        logout: {
            endpoint: `${API_BASE_URL}/authn/logout`,
            method: 'POST',
            request_payload: {} as LogoutRequest,
            response_payload: {} as LogoutResponse,
        },
    },
    user: {
        profile_pull: {
            endpoint: `${API_BASE_URL}/users/me`,
            method: 'GET',
            request_payload: {} as ProfilePullRequest,
            response_payload: {} as ProfileResponse,
        },
        profile_push: {
            endpoint: `${API_BASE_URL}/users/me`,
            method: 'PATCH',
            request_payload: {} as ProfilePushRequest,
            response_payload: {} as ProfileResponse,
        },
        profile_upload_icon: {
            endpoint: `${API_BASE_URL}/users/me/profile-picture`,
            method: 'POST',
            request_payload: {} as ProfileUploadIconRequest,
            response_payload: {} as ProfileResponse,
        },
        change_phone_otp_initiate: {
            endpoint: `${API_BASE_URL}/users/me/phone/initiate-change`,
            method: 'POST',
            request_payload: {} as ChangePhoneOtpInitiateRequest,
            response_payload: {} as PhoneOtpInitiateResponse,
        },
        change_phone_otp_verify: {
            endpoint: `${API_BASE_URL}/users/me/phone/verify-change`,
            method: 'POST',
            request_payload: {} as ChangePhoneOtpVerifyRequest,
            response_payload: {} as ChangePhoneOtpVerifyResponse,
        },
        organization_list_pull: {
            endpoint: `${API_BASE_URL}/users/me/organizations`,
            method: 'GET',
            request_payload: {} as OrganizationListPullRequest,
            response_payload: {} as OrganizationListPayload[],
        },
        statistics_pull: {
            endpoint: `${API_BASE_URL}/users/me/stats`,
            method: 'GET',
            request_payload: {} as StatisticsPullRequest,
            response_payload: {} as StatisticsPullResponse,
        },
        user_id_pull: {
            endpoint: `${API_BASE_URL}/users/me/uuid`,
            method: 'GET',
            request_payload: {} as UserIdPullRequest,
            response_payload: {} as UserIdPullResponse,
        },
        verifications_pull: {
            endpoint: `${API_BASE_URL}/users/me/verifications`,
            method: 'GET',
            request_payload: {} as VerificationsPullRequest,
            response_payload: {} as VerificationPayload[],
        },
        verifications_push: {
            endpoint: `${API_BASE_URL}/users/me/verifications`,
            method: 'POST',
            request_payload: {} as VerificationsPushRequest,
            response_payload: {} as VerificationPayload,
        },
        verifications_details_pull: {
            endpoint: `${API_BASE_URL}/users/me/verifications/{reqID}`,
            method: 'GET',
            request_payload: {} as VerificationsDetailsPullRequest,
            response_payload: {} as VerificationPayload,
        },
        volunteer_organization_qualifications_pull: {
            endpoint: `${API_BASE_URL}/users/me/volunteer/qualifications`,
            method: 'GET',
            request_payload: {} as VolunteerOrganizationQualificationsPullRequest,
            response_payload: {} as VolunteerQualificationPayload,
        },
        delete_account_initiate: {
            endpoint: `${API_BASE_URL}/users/me/delete/initiate`,
            method: 'POST',
        },
        delete_account_confirm: {
            endpoint: `${API_BASE_URL}/users/me/delete/confirm`,
            method: 'POST',
        },
        delete_account_cancel: {
            endpoint: `${API_BASE_URL}/users/me/delete/cancel`,
            method: 'POST',
        },
        delete_account_status: {
            endpoint: `${API_BASE_URL}/users/me/delete/status`,
            method: 'GET',
        },
    },
    organization: {
        organization_list_pull: {
            endpoint: `${API_BASE_URL}/organizations`,
            method: 'GET',
            request_payload: {} as OrganizationListPullRequest,
            response_payload: {} as OrganizationListPayload[],
        },
        organization_details_pull: {
            endpoint: `${API_BASE_URL}/organizations/{orgId}`,
            method: 'GET',
            request_payload: {} as OrganizationDetailsPullRequest,
            response_payload: {} as OrganizationListPayload,
        },
        organization_join: {
            endpoint: `${API_BASE_URL}/organizations/{orgId}/join`,
            method: 'POST',
            request_payload: {} as OrganizationJoinRequest,
            response_payload: {} as OrganizationJoinResponse,
        },
        organization_leave: {
            endpoint: `${API_BASE_URL}/organizations/{orgId}/leave`,
            method: 'DELETE',
            request_payload: {} as OrganizationLeaveRequest,
            response_payload: {} as OrganizationLeaveResponse,
        },
    },
    public_events: {
        published_event_list_pull: {
            endpoint: `${API_BASE_URL}/events`,
            method: 'GET',
            request_payload: {} as PublishedEventListPullRequest,
            response_payload: {} as EventListPayload[],
        },
        event_details_pull: {
            endpoint: `${API_BASE_URL}/events/{eventId}`,
            method: 'GET',
            request_payload: {} as EventDetailsPullRequest,
            response_payload: {} as EventListPayloadDetails,
        },
        event_tags_pull: {
            endpoint: `${API_BASE_URL}/event-tags`,
            method: 'GET',
            request_payload: {} as EventTagsPullRequest,
            response_payload: {} as EventTagPayload[],
        },
    },
    user_events: {
        event_check_in: {
            endpoint: `${API_BASE_URL}/event-registrations/check-in`,
            method: 'POST',
            request_payload: {} as EventCheckInRequest,
            response_payload: {} as EventRegistrationPayload,
        },
        event_register: {
            endpoint: `${API_BASE_URL}/me/event-registrations`,
            method: 'POST',
            request_payload: {} as EventRegisterRequest,
            response_payload: {} as EventRegistrationPayload,
        },
        registered_events_list_pull: {
            endpoint: `${API_BASE_URL}/me/event-registrations`,
            method: 'GET',
            request_payload: {} as RegisteredEventsListPullRequest,
            response_payload: {} as EventRegistrationPayload[],
        },
        registered_events_details_pull: {
            endpoint: `${API_BASE_URL}/me/event-registrations/{registrationId}`,
            method: 'GET',
            request_payload: {} as RegisteredEventsDetailsPullRequest,
            response_payload: {} as EventRegistrationPayload,
        },
        registered_events_cancel: {
            endpoint: `${API_BASE_URL}/me/event-registrations/{registrationId}/cancel`,
            method: 'PATCH',
            request_payload: {} as RegisteredEventsCancelRequest,
            response_payload: {} as EventRegistrationPayload,
        },
    },
    posts: {
        published_posts_list_pull: {
            endpoint: `${API_BASE_URL}/posts`,
            method: 'GET',
            request_payload: {} as PublishedPostsListPullRequest,
            response_payload: {} as PostListPayload[],
        },
        post_details_pull: {
            endpoint: `${API_BASE_URL}/posts/{postId}`,
            method: 'GET',
            request_payload: {} as PostDetailsPullRequest,
            response_payload: {} as PostListPayload,
        },
        post_tags_pull: {
            endpoint: `${API_BASE_URL}/post-tags`,
            method: 'GET',
            request_payload: {} as PostTagsPullRequest,
            response_payload: {} as PostTagsPullResponse,
        },
    },
    resources: {
        resource_list_pull: {
            endpoint: `${API_BASE_URL}/resources`,
            method: 'GET',
            request_payload: {} as ResourceListPullRequest,
            response_payload: {} as ResourceListPayload[],
        },
        resource_details_pull: {
            endpoint: `${API_BASE_URL}/resources/{resourceId}`,
            method: 'GET',
            request_payload: {} as ResourceDetailsPullRequest,
            response_payload: {} as ResourceListPayload,
        },
        resource_download: {
            endpoint: `${API_BASE_URL}/resources/download/{orgId}/{resourceIdOrSlug}/{fileIdOrName}`,
            method: 'GET',
            request_payload: {} as ResourceDownloadRequest,
            response_payload: {} as ResourceDownloadResponse,
        },
    },
    volunteer: {
        volunteer_organization_qualifications_apply : {
            endpoint: `${API_BASE_URL}/organizations/{orgId}/volunteer/apply`,
            method: 'POST',
            request_payload: {} as VolunteerOrganizationQualificationsApplyRequest,
            response_payload: {} as VolunteerOrganizationQualificationsApplyResponse,
        },
        volunteer_organization_qualifications_withdraw: {
            endpoint: `${API_BASE_URL}/volunteer/applications/{appId}/withdraw`,
            method: 'POST',
            request_payload: {} as VolunteerOrganizationQualificationsWithdrawRequest,
            response_payload: {} as VolunteerQualificationPayload,
        },
        event_volunteer_apply: {
            endpoint: `${API_BASE_URL}/events/{eventId}/volunteer-applications`,
            method: 'POST',
            request_payload: {} as EventVolunteerApplyRequest,
            response_payload: {} as EventVolunteerApplyResponse,
        },
        event_volunteer_withdraw: {
            endpoint: `${API_BASE_URL}/event/applications/{appId}/withdraw`,
            method: 'PATCH',
            request_payload: {} as EventVolunteerWithdrawRequest,
            response_payload: {} as EventVolunteerApplicationPayload,
        },
        user_event_volunteer_applications_list_pull: {
            endpoint: `${API_BASE_URL}/users/me/event-volunteer-applications`,
            method: 'GET',
            request_payload: {} as UserEventVolunteerApplicationsListPullRequest,
            response_payload: {} as EventVolunteerApplicationPayload,
        },
        user_event_volunteer_application_details_pull: {
            endpoint: `${API_BASE_URL}/users/me/event-volunteer-applications/{appId}`,
            method: 'GET',
            request_payload: {} as UserEventVolunteerApplicationDetailsPullRequest,
            response_payload: {} as EventVolunteerApplicationPayload,
        },
        // Volunteer assistance for verification
        volunteers_submit_verification: {
            endpoint: `${API_BASE_URL}/volunteers/users/verifications`,
            method: 'POST',
            request_payload: {} as FormData, // multipart/form-data with user_id and verification_type in data field
            response_payload: {} as VerificationPayload,
        },
        volunteers_get_verifications: {
            endpoint: `${API_BASE_URL}/volunteers/users/verifications`,
            method: 'GET',
            request_payload: {} as VolunteerGetVerificationsRequest,
            response_payload: {} as VerificationPayload[],
        },
    },
    admin: {
        volunteer_qualifications: {
            endpoint: `${API_BASE_URL}/admin/volunteer-qualifications`,
            method: 'GET',
        },
    },
    utils: {
        verification_types_pull: {
            endpoint: `${API_BASE_URL}/verification-types`,
            method: 'GET',
            request_payload: {} as VerificationTypesPullRequest,
            response_payload: {} as TypesPullResponse,
        },
        government_funding_types_pull: {
            endpoint: `${API_BASE_URL}/government-funding-types`,
            method: 'GET',
            request_payload: {} as GovernmentFundingTypesPullRequest,
            response_payload: {} as TypesPullResponse,
        },
    },
}