import {
  useQuery,
  useInfiniteQuery,
  useQueryClient,
} from '@tanstack/react-query';
import axiosInstance from './axios_instance';
import { AxiosError } from 'axios';
import { authenticationStore } from '@/stores/authentication_store';
import {
  fetchIntervalMs,
  ApiConfig,
  type PublishedPostsListPullRequest,
  type PostDetailsPullRequest,
  type PostTagsPullRequest,
  type PostListPayload,
  type PostTagsPullResponse,
  type PostTagPayload,
} from '@/api/api_config';
import {
  postsDashboardListStore,
  postsExploreListStore,
  postDetailsStore,
  postTagsStore,
} from 'stores/posts_store';
import { useEffect } from 'react';
import { useTranslation } from 'react-i18next';

// Parameter mapping function for posts API (only for posts, not other APIs)
const mapPostsApiParams = (params?: PublishedPostsListPullRequest) => {
  if (!params) return params;
  
  const mappedParams: any = { ...params };
  
  // Map parameter names to match webapp's API format
  if (params.tagIds) {
    mappedParams.tag_ids = Array.isArray(params.tagIds) 
      ? params.tagIds.join(',')  // Convert array to comma-separated string like webapp
      : params.tagIds;
    delete mappedParams.tagIds;
  }
  
  if (params.organization_id) {
    mappedParams.org_id = params.organization_id;
    delete mappedParams.organization_id;
  }
  
  return mappedParams;
};

// Fetch Published Posts List
export const useFetchDashboardPosts = (requestParams?: PublishedPostsListPullRequest) => {
  const queryClient = useQueryClient();
  const queryKeyConst = ['publishedPostsList', requestParams] as const;
  
  const queryResult = useQuery<PostListPayload[], Error, PostListPayload[], typeof queryKeyConst>({
    queryKey: queryKeyConst, 
    queryFn: async () => {
      try {
        const mappedParams = mapPostsApiParams(requestParams);
        const response = await axiosInstance.request<PostListPayload[]>({
          url: ApiConfig.posts.published_posts_list_pull.endpoint,
          method: ApiConfig.posts.published_posts_list_pull.method,
          params: mappedParams,
          headers: { 'Content-Type': 'application/json' },
        });
        return response.data;
      } catch (error) {
        throw error;
      }
    },
  });

  useEffect(() => {
    const store = postsDashboardListStore.getState();
    if (queryResult.isLoading) {
      // store.setIsFetching(true); // Lawson: 保持和eventCard一样的行为，我们从store中获取数据，不需要loading状态
    } else if (queryResult.isError && queryResult.error) {
      postsDashboardListStore.getState().setError(queryResult.error as any);
      // Token handling is now managed by axios interceptor
    } else if (queryResult.isSuccess && queryResult.data) {
      store.setPostsList(queryResult.data);
      store.setIsFetching(false);
      store.setError(null);
    }
  }, [queryResult.isLoading, queryResult.isError, queryResult.isSuccess, queryResult.data, queryResult.error, requestParams]);

  return queryResult;
};

export const useFetchExplorePosts = (requestParams?: PublishedPostsListPullRequest) => {
  const queryClient = useQueryClient();
  const queryKeyConst = ['explorePostsList', requestParams] as const;
  
  const queryResult = useQuery<PostListPayload[], Error, PostListPayload[], typeof queryKeyConst>({
    queryKey: queryKeyConst,
    queryFn: async () => {
      try {
        // Map parameters to match webapp's API format
        const mappedParams = mapPostsApiParams(requestParams);
        
        const response = await axiosInstance.request<PostListPayload[]>({
          url: ApiConfig.posts.published_posts_list_pull.endpoint,
          method: ApiConfig.posts.published_posts_list_pull.method,
          params: mappedParams,
          headers: { 'Content-Type': 'application/json' },
        });
        return response.data;
      } catch (error) {
        throw error;
      }
    },
  });

  useEffect(() => {
    const store = postsExploreListStore.getState();
    if (queryResult.isLoading) {
      store.setIsFetching(true);
    } else if (queryResult.isError && queryResult.error) {
      postsExploreListStore.getState().setError(queryResult.error as any);
      // Token handling is now managed by axios interceptor
    } else if (queryResult.isSuccess && queryResult.data) {
      store.setPostsList(queryResult.data);
      store.setIsFetching(false);
      store.setError(null);
    }
  }, [queryResult.isLoading, queryResult.isError, queryResult.isSuccess, queryResult.data, queryResult.error, requestParams]);

  return queryResult;
};

// Fetch Post Details
export const useFetchPostDetails = (requestParams: PostDetailsPullRequest) => {
  const queryClient = useQueryClient();
  const { postId } = requestParams;
  const queryKeyConst = ['postDetails', postId] as const;

  const queryResult = useQuery<PostListPayload, Error, PostListPayload, typeof queryKeyConst>({
    queryKey: queryKeyConst,
    queryFn: async () => {
      if (!postId) throw new Error('[useFetchPostDetails] postId is required.');
      try {
        const response = await axiosInstance.request<PostListPayload>({
          url: ApiConfig.posts.post_details_pull.endpoint.replace('{postId}', postId),
          method: ApiConfig.posts.post_details_pull.method,
          headers: { 'Content-Type': 'application/json' },
        });
        return response.data;
      } catch (error) {
        throw error;
      }
    },
    enabled: !!postId,
  });

  useEffect(() => {
    const store = postDetailsStore.getState();
    if (queryResult.isLoading) {
      store.setIsFetching(true);
    } else if (queryResult.isError && queryResult.error) {
      postDetailsStore.getState().setError(queryResult.error as any);
      // Token handling is now managed by axios interceptor
    } else if (queryResult.isSuccess && queryResult.data) {
      store.setPostDetails(queryResult.data);
      store.setIsFetching(false);
      store.setError(null);
      // console.log(`[posts_services] postDetailsStore setPostDetails successful`);
    }
  }, [queryResult.isLoading, queryResult.isError, queryResult.isSuccess, queryResult.data, queryResult.error, postId, requestParams]);

  return queryResult;
};

// Fetch Post Tags
export const useFetchPostTags = (requestParams?: PostTagsPullRequest) => {
  const queryClient = useQueryClient();
  const { i18n } = useTranslation();
  const queryKeyConst = ['postTags', requestParams, i18n.language] as const;

  const queryResult = useQuery<PostTagPayload[], Error, PostTagPayload[], typeof queryKeyConst>({
    queryKey: queryKeyConst,
    queryFn: async () => {
      try {
        const response = await axiosInstance.request<PostTagPayload[]>({
          url: ApiConfig.posts.post_tags_pull.endpoint,
          method: ApiConfig.posts.post_tags_pull.method,
          params: requestParams,
          headers: { 'Content-Type': 'application/json' },
        });
        return response.data;
      } catch (error) {
        throw error;
      }
    },
  });

  useEffect(() => {
    const store = postTagsStore.getState();
    if (queryResult.isLoading) {
      store.setIsFetching(true);
    } else if (queryResult.isError && queryResult.error) {
      postTagsStore.getState().setError(queryResult.error as any);
      // Token handling is now managed by axios interceptor
    } else if (queryResult.isSuccess && queryResult.data) {
      // API now returns tags with format: { id, name_en, name_zh_hk, name_zh_cn }
      // No need to filter by language - all languages are in one response
      const filteredTags = queryResult.data.filter(tag => 
        tag.id && 
        (tag.name_en || tag.name_zh_hk || tag.name_zh_cn)
      );
      
      // Wrap the array in the expected PostTagsPullResponse format for the store
      const wrappedResponse: PostTagsPullResponse = {
        tags: filteredTags
      };
      
      store.setPostTags(wrappedResponse);
      store.setIsFetching(false);
      store.setError(null);
    }
  }, [queryResult.isLoading, queryResult.isError, queryResult.isSuccess, queryResult.data, queryResult.error, requestParams, i18n.language]);

  return queryResult;
};
