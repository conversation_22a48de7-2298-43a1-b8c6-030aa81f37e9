import { create } from 'zustand';
import { AxiosError } from 'axios';
import type {
    TypesPullResponse, // This is used by both verification and government funding types
} from '@/api/api_config';
import { persist, createJSONStorage } from 'zustand/middleware';
import AsyncStorage from '@react-native-async-storage/async-storage';

// State for Verification Types
interface VerificationTypesState {
    verificationTypes: TypesPullResponse;
    isFetching: boolean;
    error: AxiosError | null;
    setVerificationTypes: (types: TypesPullResponse) => void;
    setError: (error: AxiosError | null) => void;
    setIsFetching: (isFetching: boolean) => void;
}

export const verificationTypesStore = create<VerificationTypesState>()(persist<VerificationTypesState>(
    (set) => ({
        verificationTypes: { types: [] },
        isFetching: false,
        error: null,
        setVerificationTypes: (types: TypesPullResponse) => set({ verificationTypes: types, isFetching: false, error: null }),
        setError: (error: AxiosError | null) => set({ error: error, isFetching: false }),
        setIsFetching: (isFetching: boolean) => set({ isFetching: isFetching }),
    }),
    {
        name: 'verification-types-storage',
        storage: createJSONStorage(() => AsyncStorage),
    }
));

// State for Government Funding Types
interface GovernmentFundingTypesState {
    governmentFundingTypes: TypesPullResponse;
    isFetching: boolean;
    error: AxiosError | null;
    setGovernmentFundingTypes: (types: TypesPullResponse) => void;
    setError: (error: AxiosError | null) => void;
    setIsFetching: (isFetching: boolean) => void;
}

export const governmentFundingTypesStore = create<GovernmentFundingTypesState>()(persist<GovernmentFundingTypesState>(
    (set) => ({
        governmentFundingTypes: { types: [] },
        isFetching: false,
        error: null,
        setGovernmentFundingTypes: (types: TypesPullResponse) => set({ governmentFundingTypes: types, isFetching: false, error: null }),
        setError: (error: AxiosError | null) => set({ error: error, isFetching: false }),
        setIsFetching: (isFetching: boolean) => set({ isFetching: isFetching }),
    }),
    {
        name: 'government-funding-types-storage',
        storage: createJSONStorage(() => AsyncStorage),
    }
));
