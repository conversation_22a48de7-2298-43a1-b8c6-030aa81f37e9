import { create } from 'zustand';
import { createTheme } from '@/theme/index';
import { persist, createJSONStorage } from 'zustand/middleware';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { env } from '@/config/environment';

interface appStyleState {
    theme: any;
    currentThemeName: string;
    setTheme: (themeName: string) => void;
}

const initialThemeName = env.ui.defaultThemeColor;

export const appStyleStore = create<appStyleState>()(
    persist(
        (set, get) => ({
            theme: createTheme(initialThemeName),
            currentThemeName: initialThemeName,
            setTheme: (themeName: string) => {
                if (themeName !== get().currentThemeName) {
                    set({ theme: createTheme(themeName), currentThemeName: themeName });
                }
            },
        }),
        {
            name: `${env.storage.keyPrefix}app-style-storage`, // name of the item in the storage (must be unique)
            storage: createJSONStorage(() => AsyncStorage), // (optional) by default, 'localStorage' is used
        }
    )
);