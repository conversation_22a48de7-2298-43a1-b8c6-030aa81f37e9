import 'dotenv/config';

/**
 * Expo Configuration with Environment Variables
 * 
 * This configuration file loads environment variables from .env files
 * and makes them available to the app through Constants.expoConfig.extra
 */

export default ({ config }) => {
  // Get environment from APP_ENV or default to development
  const appEnv = process.env.APP_ENV || 'development';
  
  // App display name with environment suffix for non-production
  const getAppName = () => {
    const baseName = process.env.APP_DISPLAY_NAME || 'THE Moment';
    if (appEnv === 'development') return `${baseName} (Dev)`;
    if (appEnv === 'staging') return `${baseName} (Staging)`;
    return baseName;
  };

  // Bundle identifier with environment suffix for non-production
  const getBundleId = () => {
    const baseBundleId = process.env.APP_BUNDLE_ID || 'com.themoment.app';
    if (appEnv === 'development') return `${baseBundleId}.dev`;
    if (appEnv === 'staging') return `${baseBundleId}.staging`;
    return baseBundleId;
  };

  // App scheme with environment suffix
  const getAppScheme = () => {
    if (appEnv === 'development') return 'themoment-dev';
    if (appEnv === 'staging') return 'themoment-staging';
    return 'themoment';
  };

  return {
    ...config,
    name: getAppName(),
    slug: 'the-moment',
    scheme: getAppScheme(),
    version: process.env.APP_VERSION || '1.0.0',
    orientation: 'portrait',
    icon: './assets/icon.png',
    userInterfaceStyle: 'light',
    newArchEnabled: true,
    splash: {
      image: './assets/splash-icon.png',
      resizeMode: 'contain',
      backgroundColor: '#ffffff'
    },
    ios: {
      supportsTablet: true,
      bundleIdentifier: getBundleId(),
      infoPlist: {
        ITSAppUsesNonExemptEncryption: false,
      },
      // Add environment-specific configuration
      ...(appEnv === 'production' && {
        buildNumber: process.env.IOS_BUILD_NUMBER || '1',
      }),
    },
    android: {
      adaptiveIcon: {
        foregroundImage: './assets/adaptive-icon.png',
        backgroundColor: '#ffffff'
      },
      package: getBundleId(),
      // Add environment-specific configuration
      ...(appEnv === 'production' && {
        versionCode: parseInt(process.env.ANDROID_VERSION_CODE || '1', 10),
      }),
    },
    web: {
      favicon: './assets/favicon.png',
      bundler: 'metro',
    },
    plugins: [
      'expo-router',
      // Conditionally add plugins based on environment
      ...(process.env.FEATURE_ENABLE_PUSH_NOTIFICATIONS === 'true' ? [
        [
          'expo-notifications',
          {
            icon: './assets/notification-icon.png',
            color: '#ffffff',
          }
        ]
      ] : []),
    ],
    // Expo Updates configuration
    updates: {
      enabled: process.env.EXPO_USE_UPDATES === 'true',
      url: "https://u.expo.dev/98f7f685-9c49-419a-8957-acbfc0b4bce0",
      ...(process.env.EXPO_UPDATE_URL && {
        url: process.env.EXPO_UPDATE_URL,
      }),
    },
    runtimeVersion: {
      policy: "appVersion"
    },
    // Development-specific configuration
    ...(appEnv === 'development' && {
      developmentClient: {
        silentLaunch: true,
      },
    }),
    // Make environment variables available to the app
    extra: {
      ...config.extra,
      // Application Environment
      APP_ENV: process.env.APP_ENV || 'development',
      APP_DEBUG: process.env.APP_DEBUG || 'true',
      APP_VERSION: process.env.APP_VERSION || '1.0.0',

      // API Configuration
      API_URL: process.env.API_URL || process.env.EXPO_PUBLIC_API_BASE,
      API_TIMEOUT: process.env.EXPO_PUBLIC_API_TIMEOUT || '30000',
      API_REFETCH_INTERVAL: process.env.EXPO_PUBLIC_API_REFETCH_INTERVAL || '60000',
      FILE_SERVER_URL: process.env.FILE_SERVER_URL || process.env.EXPO_PUBLIC_MEDIA_BASE,

      // Authentication & Security
      AUTH_CLIENT_ID: process.env.EXPO_PUBLIC_AUTH_CLIENT_ID || 'mobile-app-client',
      AUTH_REDIRECT_URI: process.env.AUTH_REDIRECT_URI || `${getAppScheme()}://auth`,
      AUTH_REFRESH_THRESHOLD_MINUTES: process.env.EXPO_PUBLIC_AUTH_REFRESH_THRESHOLD_MINUTES || '5',

      // Internationalization
      DEFAULT_LANGUAGE: process.env.EXPO_PUBLIC_DEFAULT_LANGUAGE || 'zh-HK',
      FALLBACK_LANGUAGE: process.env.EXPO_PUBLIC_FALLBACK_LANGUAGE || 'en',
      I18N_DEBUG: process.env.EXPO_PUBLIC_I18N_DEBUG || (appEnv === 'development' ? 'true' : 'false'),

      // Feature Flags
      FEATURE_ENABLE_ANALYTICS: process.env.EXPO_PUBLIC_FEATURE_ENABLE_ANALYTICS || 'false',
      FEATURE_ENABLE_PUSH_NOTIFICATIONS: process.env.EXPO_PUBLIC_FEATURE_ENABLE_PUSH_NOTIFICATIONS || 'true',
      FEATURE_ENABLE_BIOMETRIC_AUTH: process.env.EXPO_PUBLIC_FEATURE_ENABLE_BIOMETRIC_AUTH || 'false',
      FEATURE_ENABLE_DEBUG_MENU: process.env.EXPO_PUBLIC_FEATURE_ENABLE_DEBUG_MENU || (appEnv === 'development' ? 'true' : 'false'),
      FEATURE_MOCK_MODE: process.env.EXPO_PUBLIC_FEATURE_MOCK_MODE || 'false',

      // UI & UX
      DEFAULT_THEME_COLOR: process.env.EXPO_PUBLIC_DEFAULT_THEME_COLOR || 'red',
      ANIMATION_SPEED_MULTIPLIER: process.env.EXPO_PUBLIC_ANIMATION_SPEED_MULTIPLIER || '1.0',
      ENABLE_HAPTIC_FEEDBACK: process.env.EXPO_PUBLIC_ENABLE_HAPTIC_FEEDBACK || 'true',
      FONT_SCALE_FACTOR: process.env.EXPO_PUBLIC_FONT_SCALE_FACTOR || '1.0',

      // Development & Debugging
      ENABLE_REACT_QUERY_DEVTOOLS: process.env.EXPO_PUBLIC_ENABLE_REACT_QUERY_DEVTOOLS || (appEnv === 'development' ? 'true' : 'false'),
      ENABLE_STATE_DEVTOOLS: process.env.EXPO_PUBLIC_ENABLE_STATE_DEVTOOLS || (appEnv === 'development' ? 'true' : 'false'),
      ENABLE_NETWORK_LOGGING: process.env.EXPO_PUBLIC_ENABLE_NETWORK_LOGGING || (appEnv === 'development' ? 'true' : 'false'),
      ENABLE_FLIPPER: process.env.EXPO_PUBLIC_ENABLE_FLIPPER || (appEnv === 'development' ? 'true' : 'false'),
      ENABLE_PERFORMANCE_MONITORING: process.env.EXPO_PUBLIC_ENABLE_PERFORMANCE_MONITORING || 'false',

      // Storage & Caching
      STORAGE_KEY_PREFIX: process.env.EXPO_PUBLIC_STORAGE_KEY_PREFIX || 'themoment_',
      CACHE_DURATION_MINUTES: process.env.EXPO_PUBLIC_CACHE_DURATION_MINUTES || '60',
      CACHE_MAX_SIZE_MB: process.env.EXPO_PUBLIC_CACHE_MAX_SIZE_MB || '100',

      // Security
      ENABLE_CERTIFICATE_PINNING: process.env.EXPO_PUBLIC_ENABLE_CERTIFICATE_PINNING || (appEnv === 'production' ? 'true' : 'false'),
      ENABLE_SSL_VERIFICATION: process.env.EXPO_PUBLIC_ENABLE_SSL_VERIFICATION || 'true',
      REQUIRE_BIOMETRIC_AUTH: process.env.EXPO_PUBLIC_REQUIRE_BIOMETRIC_AUTH || 'false',
      SESSION_TIMEOUT_MINUTES: process.env.EXPO_PUBLIC_SESSION_TIMEOUT_MINUTES || '60',

      // Notifications
      DEFAULT_ENABLE_APP_NOTIFICATIONS: process.env.EXPO_PUBLIC_DEFAULT_ENABLE_APP_NOTIFICATIONS || 'true',
      DEFAULT_ENABLE_WHATSAPP_NOTIFICATIONS: process.env.EXPO_PUBLIC_DEFAULT_ENABLE_WHATSAPP_NOTIFICATIONS || 'true',

      // Testing
      TEST_MODE: process.env.EXPO_PUBLIC_TEST_MODE || 'false',
      MOCK_API_RESPONSES: process.env.EXPO_PUBLIC_MOCK_API_RESPONSES || 'false',
      TEST_USER_PHONE: process.env.EXPO_PUBLIC_TEST_USER_PHONE,
      TEST_USER_OTP: process.env.EXPO_PUBLIC_TEST_USER_OTP,

      // Logging
      LOG_LEVEL: process.env.EXPO_PUBLIC_LOG_LEVEL || 'debug',
      ENABLE_REMOTE_LOGGING: process.env.EXPO_PUBLIC_ENABLE_REMOTE_LOGGING || 'false',

      // Performance
      IMAGE_CACHE_SIZE_MB: process.env.EXPO_PUBLIC_IMAGE_CACHE_SIZE_MB || '50',
      MAX_CONCURRENT_REQUESTS: process.env.EXPO_PUBLIC_MAX_CONCURRENT_REQUESTS || '6',
      NETWORK_RETRY_ATTEMPTS: process.env.EXPO_PUBLIC_NETWORK_RETRY_ATTEMPTS || '3',

      // Accessibility
      ENABLE_ACCESSIBILITY_FEATURES: process.env.EXPO_PUBLIC_ENABLE_ACCESSIBILITY_FEATURES || 'true',
      DEFAULT_FONT_SIZE: process.env.EXPO_PUBLIC_DEFAULT_FONT_SIZE || 'medium',
      ENABLE_HIGH_CONTRAST: process.env.EXPO_PUBLIC_ENABLE_HIGH_CONTRAST || 'false',
      ENABLE_REDUCED_MOTION: process.env.EXPO_PUBLIC_ENABLE_REDUCED_MOTION || 'false',

      // Third-party Services
      SERVICE_GOOGLE_MAPS_API_KEY: process.env.EXPO_PUBLIC_SERVICE_GOOGLE_MAPS_API_KEY,
      SERVICE_FIREBASE_PROJECT_ID: process.env.EXPO_PUBLIC_SERVICE_FIREBASE_PROJECT_ID,
      SERVICE_FIREBASE_MESSAGING_SENDER_ID: process.env.EXPO_PUBLIC_SERVICE_FIREBASE_MESSAGING_SENDER_ID,
      SERVICE_FIREBASE_APP_ID: process.env.EXPO_PUBLIC_SERVICE_FIREBASE_APP_ID,
      SERVICE_ANALYTICS_API_KEY: process.env.EXPO_PUBLIC_SERVICE_ANALYTICS_API_KEY,
      SERVICE_ANALYTICS_ENDPOINT: process.env.EXPO_PUBLIC_SERVICE_ANALYTICS_ENDPOINT,

      // URLs
      PRIVACY_POLICY_URL: process.env.EXPO_PUBLIC_PRIVACY_POLICY_URL || 'https://example.com/privacy',
      TERMS_OF_SERVICE_URL: process.env.EXPO_PUBLIC_TERMS_OF_SERVICE_URL || 'https://example.com/terms',

      // Expo specific
      EXPO_PROJECT_ID: process.env.EXPO_PROJECT_ID,
      EXPO_USE_UPDATES: process.env.EXPO_USE_UPDATES || 'true',
      EXPO_UPDATE_URL: process.env.EXPO_UPDATE_URL,
    }
  };
};