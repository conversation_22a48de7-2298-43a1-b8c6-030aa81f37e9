import { authenticationStore } from '@/stores/authentication_store';
import { userProfileStore, userOrganizationsStore, userVerificationsStore } from '@/stores/user_store';
import axios from 'axios';
import { API_BASE_URL } from '@/api/api_config';
import { ApiConfig } from '@/api/api_config';
import { env } from '@/config/environment';
import type { AccessTokenRefreshResponse } from '@/api/api_config';
import { QueryClient } from '@tanstack/react-query';

let isRefreshing = false;
let refreshPromise: Promise<void> | null = null;

/**
 * Unified logout function that clears all authentication state
 */
export const performLogout = () => {
    if (env.development.enableNetworkLogging) {
        console.log('[AuthUtils] Performing logout - clearing all auth state');
    }

    // Clear authentication store
    authenticationStore.getState().logout();

    // Clear user-related stores
    userProfileStore.getState().setProfile(null);
    userOrganizationsStore.getState().setUserOrganizations([]);
    userVerificationsStore.getState().setVerifications([]);
};

/**
 * Unified token refresh function that prevents race conditions
 */
export const attemptTokenRefresh = async (queryClient?: QueryClient): Promise<boolean> => {
    // If already refreshing, wait for the existing promise
    if (isRefreshing && refreshPromise) {
        try {
            await refreshPromise;
            return authenticationStore.getState().isAuthenticated;
        } catch {
            return false;
        }
    }

    const { refreshToken } = authenticationStore.getState();

    if (!refreshToken) {
        if (env.development.enableNetworkLogging) {
            console.log('[AuthUtils] No refresh token found, performing logout');
        }
        performLogout();
        return false;
    }

    isRefreshing = true;
    refreshPromise = (async () => {
        try {
            if (env.development.enableNetworkLogging) {
                console.log('[AuthUtils] Attempting token refresh');
            }
            const refreshResponse = await axios.post<AccessTokenRefreshResponse>(
                `${API_BASE_URL}${ApiConfig.authentication.access_token_refresh.endpoint}`,
                { refresh_token: refreshToken },
                { headers: { 'Content-Type': 'application/json' } }
            );

            if (refreshResponse.data.access_token && refreshResponse.data.refresh_token) {
                authenticationStore.getState().setAccessToken(refreshResponse.data.access_token);
                authenticationStore.getState().setRefreshToken(refreshResponse.data.refresh_token);
                authenticationStore.getState().setIsAuthorized(true);
                if (env.development.enableNetworkLogging) {
                    console.log('[AuthUtils] Token refresh successful');
                }

                // Invalidate relevant queries if queryClient is provided
                if (queryClient) {
                    await queryClient.invalidateQueries({ queryKey: ['userProfile'] });
                    await queryClient.invalidateQueries({ queryKey: ['userOrganizations'] });
                    await queryClient.invalidateQueries({ queryKey: ['userVerifications'] });
                }

                return;
            } else {
                throw new Error('Invalid refresh response');
            }
        } catch (error) {
            console.error('[AuthUtils] Token refresh failed:', error);
            performLogout();
            throw error;
        }
    })();

    try {
        await refreshPromise;
        return true;
    } catch {
        return false;
    } finally {
        isRefreshing = false;
        refreshPromise = null;
    }
};

/**
 * Check if an error is a 401 authentication error
 */
export const isAuthenticationError = (error: any): boolean => {
    return axios.isAxiosError(error) && error.response?.status === 401;
};

/**
 * Handle API authentication errors with unified logic
 */
export const handleAuthenticationError = async (
    error: any,
    serviceName: string,
    queryClient?: QueryClient
): Promise<boolean> => {
    if (!isAuthenticationError(error)) {
        return false;
    }

    console.log(`[AuthUtils] ${serviceName} detected 401 error, attempting refresh`);

    try {
        const refreshSuccess = await attemptTokenRefresh(queryClient);
        if (refreshSuccess) {
            console.log(`[AuthUtils] ${serviceName} token refresh successful`);
            return true;
        } else {
            console.log(`[AuthUtils] ${serviceName} token refresh failed, user logged out`);
            return false;
        }
    } catch {
        console.log(`[AuthUtils] ${serviceName} token refresh failed, user logged out`);
        return false;
    }
}; 