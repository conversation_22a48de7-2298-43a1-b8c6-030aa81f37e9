import { MEDIA_BASE_URL } from '@/api/api_config';

/**
 * Convert relative image path to absolute URL
 * @param filePath - The file path from API response
 * @returns Complete URL or undefined if path is invalid
 */
export const getValidImageUrl = (filePath: string | undefined): string | undefined => {
  if (!filePath) return undefined;
  try {
    const url = new URL(filePath, MEDIA_BASE_URL);
    return new URL(url.pathname, MEDIA_BASE_URL).toString();
  } catch (error) {
    console.warn('Error processing image URL:', filePath, error);
    return undefined;
  }
};

/**
 * Process event object to convert all image URLs to absolute URLs
 * @param event - Event object from API
 * @returns Event with processed image URLs
 */
export const processEventImageUrls = <T extends { banner_image_urls?: string[]; media_items?: Array<{ file_path: string; [key: string]: any }> }>(
  event: T
): T & { processed_image_url?: string } => {
  if (!event) return event as T & { processed_image_url?: string };

  const processedEvent = { ...event };

  // Process banner_image_urls
  if (processedEvent.banner_image_urls && Array.isArray(processedEvent.banner_image_urls)) {
    processedEvent.banner_image_urls = processedEvent.banner_image_urls.map(url => getValidImageUrl(url)).filter(Boolean) as string[];
  }

  // Process media_items
  if (processedEvent.media_items && Array.isArray(processedEvent.media_items)) {
    processedEvent.media_items = processedEvent.media_items.map(item => {
      if (item && item.file_path) {
        return { ...item, file_path: getValidImageUrl(item.file_path) || item.file_path };
      }
      return item;
    });
  }

  // Determine a primary display image URL
  let primaryImageUrl: string | undefined;
  if (processedEvent.banner_image_urls && processedEvent.banner_image_urls.length > 0) {
    primaryImageUrl = processedEvent.banner_image_urls[0];
  } else if (processedEvent.media_items && processedEvent.media_items.length > 0 && processedEvent.media_items[0]?.file_path) {
    // Check if the first media item is an image type
    const firstItem = processedEvent.media_items[0];
    if (!firstItem.file_type || firstItem.file_type.startsWith('image/')) {
      primaryImageUrl = firstItem.file_path;
    }
  }

  return {
    ...processedEvent,
    processed_image_url: primaryImageUrl
  };
}; 