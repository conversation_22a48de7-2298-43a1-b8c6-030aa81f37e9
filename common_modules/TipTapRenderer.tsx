import React from "react";
import { Text, TextStyle, View, StyleSheet, Platform } from "react-native";
import * as Linking from 'expo-linking';

// Type definitions
export interface TipTapMark {
  type: string;
  attrs?: {
    color?: string;
    href?: string;
    [key: string]: any;
  };
}

export interface TipTapNode {
  type: string;
  from?: number;
  to?: number;
  text?: string;
  marks?: TipTapMark[];
  content?: TipTapNode[];
  attrs?: {
    level?: number;
    textAlign?: 'left' | 'center' | 'right';
    [key: string]: any;
  };
}

export type NodeHandler = React.FC<{
  node?: TipTapNode;
  children?: React.ReactNode;
}>;

export interface NodeHandlers {
  [key: string]: NodeHandler;
}

// Component to render TipTap nodes
export const TipTapRenderer: React.FC<{
  node: TipTapNode;
  handlers: NodeHandlers;
}> = ({ node, handlers }) => {
  if (!node) return null;

  // If this is a text node, use the text handler
  if (node.type === "text" && handlers.text) {
    // Skip empty text nodes
    if (!node.text || node.text.trim() === '') return null;
    return handlers.text({ node });
  }

  // For any other node type, render children if there are any
  const Handler = handlers[node.type];
  if (!Handler) {
    console.warn(`No handler for node type: ${node.type}`);
    return null;
  }

  // Skip rendering if there's no content in an array type node
  if (node.content && node.content.length === 0) {
    return null;
  }

  // Render children if there are any
  const children = node.content?.map((child, index) => (
    <TipTapRenderer key={index} node={child} handlers={handlers} />
  ));

  // If all children are null (empty), return null
  if (children && React.Children.count(children) === 0) {
    return null;
  }

  return <Handler node={node}>{children}</Handler>;
};

// Default handlers
const Doc: NodeHandler = ({ children }) => {
  // Don't render empty documents
  if (!children || React.Children.count(children) === 0) {
    return null;
  }
  return <View>{children}</View>;
};

const Paragraph: NodeHandler = ({ node, children }) => {
  const content = React.Children.toArray(children);
  
  // Don't render empty paragraphs
  if (content.length === 0) {
    return null;
  }
  
  // Handle text alignment
  const textAlign = node?.attrs?.textAlign || 'left';
  const alignmentStyle = { textAlign };
  
  return (
    <Text style={[styles.paragraph, alignmentStyle]}>
      {content}
    </Text>
  );
};

const TextNode: NodeHandler = ({ node }) => {
  if (!node || !node.text) return null;
  
  const { marks, text } = node;

  // Check if this text has a link mark
  const linkMark = marks?.find(mark => mark.type === 'link');
  if (linkMark && linkMark.attrs?.href) {
    const href = linkMark.attrs.href;
    
    // Apply other styles to the link text
    const initialStyle: TextStyle = {
      fontWeight: "normal",
      fontStyle: "normal",
      textDecorationLine: "none",
      color: "#0066CC", // Link color
    };
    
    const style = marks?.reduce<TextStyle>(
      (acc, mark) => {
        if (mark.type === 'link') return acc; // Skip link mark for styling
        
        switch (mark.type) {
          case "bold":
            return { ...acc, fontWeight: "bold" };
          case "italic":
            return { ...acc, fontStyle: "italic" };
          case "underline":
            return { ...acc, textDecorationLine: "underline" };
          case "textStyle":
            return mark.attrs?.color ? { ...acc, color: mark.attrs.color } : acc;
          default:
            return acc;
        }
      },
      initialStyle
    ) || initialStyle;
    
    return (
      <Text 
        style={[style, styles.link]} 
        onPress={() => Linking.openURL(href)}
      >
        {text}
      </Text>
    );
  }

  const initialStyle: TextStyle = {
    fontWeight: "normal",
    fontStyle: "normal",
    textDecorationLine: "none",
    color: "#333333",
  };

  const style = marks?.reduce<TextStyle>(
    (acc, mark) => {
      switch (mark.type) {
        case "bold":
          return { ...acc, fontWeight: "bold" };
        case "italic":
          return { ...acc, fontStyle: "italic" };
        case "underline":
          return { ...acc, textDecorationLine: "underline" };
        case "textStyle":
          return mark.attrs?.color ? { ...acc, color: mark.attrs.color } : acc;
        default:
          return acc;
      }
    },
    initialStyle
  ) || initialStyle;

  return <Text style={style}>{text}</Text>;
};

const Heading: NodeHandler = ({ node, children }) => {
  if (!node?.attrs?.level) return <Text>{children}</Text>;
  
  const { level } = node.attrs;
  // Handle text alignment for headings
  const textAlign = node.attrs?.textAlign || 'left';

  let fontSize;
  switch (level) {
    case 1:
      fontSize = 22;
      break;
    case 2:
      fontSize = 20;
      break;
    case 3:
      fontSize = 18;
      break;
    default:
      fontSize = 16;
      break;
  }

  return (
    <View style={styles.heading}>
      <Text style={{ 
        fontSize, 
        fontWeight: "bold", 
        marginVertical: 8,
        textAlign 
      }}>
        {children}
      </Text>
    </View>
  );
};

// List handlers
const BulletList: NodeHandler = ({ children }) => {
  // Don't render empty lists
  if (!children || React.Children.count(children) === 0) {
    return null;
  }
  return <View style={styles.bulletList}>{children}</View>;
};

// We'll use a context to pass down list type information
const ListContext = React.createContext<{
  isOrdered: boolean;
  index?: number;
}>({ isOrdered: false });

const OrderedList: NodeHandler = ({ children }) => {
  // Don't render empty lists
  if (!children || React.Children.count(children) === 0) {
    return null;
  }
  
  // Wrap children with context provider to indicate this is an ordered list
  return (
    <View style={styles.orderedList}>
      {React.Children.map(children, (child, index) => {
        if (!child) return null;
        return (
          <ListContext.Provider value={{ isOrdered: true, index: index + 1 }}>
            {child}
          </ListContext.Provider>
        );
      })}
    </View>
  );
};

interface ListItemProps {
  node?: TipTapNode;
  children?: React.ReactNode;
}

const ListItem: React.FC<ListItemProps> = ({ children, node }) => {
  // Don't render empty list items
  if (!children || React.Children.count(children) === 0) {
    return null;
  }
  
  // Get list type from context
  const { isOrdered, index } = React.useContext(ListContext);
  
  // Check if paragraph within listItem has alignment
  let textAlign: 'left' | 'center' | 'right' = 'left';
  
  // Try to find paragraph with alignment in the node's content
  if (node?.content) {
    const paragraphWithAlign = node.content.find(
      child => child.type === 'paragraph' && child.attrs?.textAlign
    );
    if (paragraphWithAlign?.attrs?.textAlign) {
      textAlign = paragraphWithAlign.attrs.textAlign;
    }
  }
  
  // Render bullet point or number based on list type
  const bulletOrNumber = isOrdered 
    ? <Text style={styles.listNumber}>{index}.</Text> 
    : <Text style={styles.bullet}>•</Text>;
  
  return (
    <View style={styles.listItem}>
      {bulletOrNumber}
      <View style={styles.listItemContent}>
        {children}
      </View>
    </View>
  );
};

// Add code block handler
const CodeBlock: NodeHandler = ({ children }) => {
  // Don't render empty code blocks
  if (!children || React.Children.count(children) === 0) {
    return null;
  }
  
  return (
    <View style={styles.codeBlock}>
      <Text style={styles.codeText}>{children}</Text>
    </View>
  );
};

// Default handlers collection
export const defaultHandlers: NodeHandlers = {
  doc: Doc,
  text: TextNode,
  paragraph: Paragraph,
  heading: Heading,
  bulletList: BulletList,
  orderedList: OrderedList,
  listItem: ListItem as NodeHandler,
  codeBlock: CodeBlock,
};

// Component style
const styles = StyleSheet.create({
  paragraph: {
    fontSize: 15,
    lineHeight: 24,
    color: "#333333",
    marginBottom: 12,
    width: '100%',
  },
  heading: {
    marginBottom: 8,
    width: '100%',
  },
  link: {
    textDecorationLine: 'underline',
  },
  bulletList: {
    marginBottom: 0,
    paddingLeft: 4,
    width: '100%',
  },
  orderedList: {
    marginBottom: 0,
    paddingLeft: 4,
    width: '100%',
  },
  listItem: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'flex-start',
    width: '100%',
  },
  listItemContent: {
    flex: 1,
    paddingLeft: 8,
  },
  bullet: {
    marginTop: 2,
    fontSize: 16,
    marginRight: 1,
    textAlign: 'center',
  },
  listNumber: {
    marginTop: 3,
    fontSize: 15,
    marginRight: 1,
    textAlign: 'right',
  },
  codeBlock: {
    backgroundColor: '#f5f5f5',
    padding: 12,
    borderRadius: 4,
    marginBottom: 16,
    width: '100%',
  },
  codeText: {
    fontFamily: Platform.OS === 'ios' ? 'Menlo' : 'monospace',
    fontSize: 14,
    color: '#333333',
  },
});

// Helper function to check if a value is a valid TipTap JSON
export const isTipTapJSON = (value: any): boolean => {
  if (typeof value !== "object") return false;
  if (!value) return false;
  
  // Basic check for TipTap structure
  return value.type === "doc" && Array.isArray(value.content);
};