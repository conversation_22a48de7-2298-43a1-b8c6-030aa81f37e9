import React, { Component, ReactNode } from 'react';
import { View, Text, StyleSheet, Alert } from 'react-native';
import { Button } from 'react-native-paper';
import { SafeAreaView } from 'react-native-safe-area-context';
import MaterialCommunityIcons from '@expo/vector-icons/MaterialCommunityIcons';
import { env } from '@/config/environment';

interface Props {
  children: ReactNode;
  fallback?: ReactNode;
}

interface State {
  hasError: boolean;
  error: Error | null;
  errorInfo: any;
}

/**
 * Error Boundary specifically designed to catch configuration validation failures
 * and other critical app initialization errors.
 */
export class ConfigurationErrorBoundary extends Component<Props, State> {
  constructor(props: Props) {
    super(props);
    this.state = {
      hasError: false,
      error: null,
      errorInfo: null
    };
  }

  static getDerivedStateFromError(error: Error): State {
    // Update state so the next render will show the fallback UI
    return {
      hasError: true,
      error,
      errorInfo: null
    };
  }

  componentDidCatch(error: Error, errorInfo: any) {
    // Log error details for debugging
    console.error('ConfigurationErrorBoundary caught an error:', error);
    console.error('Error info:', errorInfo);
    
    this.setState({
      error,
      errorInfo
    });

    // Report configuration errors in development/staging
    if (env.app.debug) {
      Alert.alert(
        'Configuration Error',
        `${error.message}\n\nStack: ${error.stack}`,
        [{ text: 'OK' }]
      );
    }
  }

  private handleReload = () => {
    // Reset the error boundary and attempt to reload
    this.setState({
      hasError: false,
      error: null,
      errorInfo: null
    });
  };

  private handleShowDetails = () => {
    if (this.state.error) {
      Alert.alert(
        'Error Details',
        `${this.state.error.message}\n\nStack: ${this.state.error.stack}`,
        [{ text: 'OK' }]
      );
    }
  };

  render() {
    if (this.state.hasError) {
      // Check if custom fallback is provided
      if (this.props.fallback) {
        return this.props.fallback;
      }

      // Default fallback UI
      return (
        <SafeAreaView style={styles.container}>
          <View style={styles.content}>
            <MaterialCommunityIcons
              name="alert-octagon"
              size={80}
              color="#E53E3E"
            />
            <Text style={styles.title}>Configuration Error</Text>
            <Text style={styles.message}>
              The app encountered a configuration error that prevents it from starting properly.
            </Text>
            
            {/* Show more details in development */}
            {env.app.debug && this.state.error && (
              <View style={styles.errorDetails}>
                <Text style={styles.errorTitle}>Error Details:</Text>
                <Text style={styles.errorText}>
                  {this.state.error.message}
                </Text>
              </View>
            )}

            <View style={styles.buttonContainer}>
              <Button
                mode="contained"
                onPress={this.handleReload}
                style={[styles.button, { backgroundColor: '#3182CE' }]}
                contentStyle={styles.buttonContent}
              >
                Reload App
              </Button>
              
              {env.app.debug && (
                <Button
                  mode="outlined"
                  onPress={this.handleShowDetails}
                  style={styles.button}
                  contentStyle={styles.buttonContent}
                >
                  Show Details
                </Button>
              )}
            </View>

            <Text style={styles.footer}>
              If this problem persists, please check your environment configuration
              or contact support.
            </Text>
          </View>
        </SafeAreaView>
      );
    }

    return this.props.children;
  }
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#F7FAFC',
  },
  content: {
    flex: 1,
    alignItems: 'center',
    justifyContent: 'center',
    paddingHorizontal: 24,
    paddingVertical: 40,
  },
  title: {
    fontSize: 24,
    fontWeight: 'bold',
    color: '#1A202C',
    marginTop: 24,
    marginBottom: 16,
    textAlign: 'center',
  },
  message: {
    fontSize: 16,
    color: '#4A5568',
    textAlign: 'center',
    lineHeight: 24,
    marginBottom: 32,
    maxWidth: 320,
  },
  errorDetails: {
    backgroundColor: '#FED7D7',
    padding: 16,
    borderRadius: 8,
    marginBottom: 24,
    maxWidth: '100%',
  },
  errorTitle: {
    fontSize: 14,
    fontWeight: '600',
    color: '#C53030',
    marginBottom: 8,
  },
  errorText: {
    fontSize: 12,
    color: '#742A2A',
    fontFamily: 'monospace',
  },
  buttonContainer: {
    gap: 12,
    width: '100%',
    maxWidth: 280,
    marginBottom: 32,
  },
  button: {
    borderRadius: 8,
  },
  buttonContent: {
    height: 48,
    justifyContent: 'center',
  },
  footer: {
    fontSize: 12,
    color: '#718096',
    textAlign: 'center',
    lineHeight: 18,
    maxWidth: 280,
  },
});