# API Configuration
EXPO_PUBLIC_API_BASE="https://the-moment-api.haytech.io/api/v1"
EXPO_PUBLIC_MEDIA_BASE="https://the-moment-api.haytech.io"
API_URL="https://the-moment-api.haytech.io/api/v1"
EXPO_PUBLIC_API_TIMEOUT="30000"
EXPO_PUBLIC_API_REFETCH_INTERVAL="60000"

# App Configuration
APP_ENV="development"
APP_DEBUG="true"
APP_VERSION="1.0.0"
APP_DISPLAY_NAME="THE Moment"
APP_BUNDLE_ID="com.hexacubic.themoment"

# Authentication
EXPO_PUBLIC_AUTH_CLIENT_ID="mobile-app-client"
EXPO_PUBLIC_AUTH_REFRESH_THRESHOLD_MINUTES="5"

# Internationalization
EXPO_PUBLIC_DEFAULT_LANGUAGE="zh-HK"
EXPO_PUBLIC_FALLBACK_LANGUAGE="en"
EXPO_PUBLIC_I18N_DEBUG="true"

# Feature Flags
EXPO_PUBLIC_FEATURE_ENABLE_ANALYTICS="false"
EXPO_PUBLIC_FEATURE_ENABLE_PUSH_NOTIFICATIONS="true"
EXPO_PUBLIC_FEATURE_ENABLE_BIOMETRIC_AUTH="false"
EXPO_PUBLIC_FEATURE_ENABLE_DEBUG_MENU="true"
EXPO_PUBLIC_FEATURE_MOCK_MODE="false"

# UI & UX
EXPO_PUBLIC_DEFAULT_THEME_COLOR="red"
EXPO_PUBLIC_ANIMATION_SPEED_MULTIPLIER="1.0"
EXPO_PUBLIC_ENABLE_HAPTIC_FEEDBACK="true"
EXPO_PUBLIC_FONT_SCALE_FACTOR="1.0"

# Development & Debugging
EXPO_PUBLIC_ENABLE_REACT_QUERY_DEVTOOLS="true"
EXPO_PUBLIC_ENABLE_STATE_DEVTOOLS="true"
EXPO_PUBLIC_ENABLE_NETWORK_LOGGING="true"
EXPO_PUBLIC_ENABLE_FLIPPER="true"
EXPO_PUBLIC_ENABLE_PERFORMANCE_MONITORING="false"

# Storage & Caching
EXPO_PUBLIC_STORAGE_KEY_PREFIX="themoment_"
EXPO_PUBLIC_CACHE_DURATION_MINUTES="60"
EXPO_PUBLIC_CACHE_MAX_SIZE_MB="100"

# Security
EXPO_PUBLIC_ENABLE_CERTIFICATE_PINNING="false"
EXPO_PUBLIC_ENABLE_SSL_VERIFICATION="true"
EXPO_PUBLIC_REQUIRE_BIOMETRIC_AUTH="false"
EXPO_PUBLIC_SESSION_TIMEOUT_MINUTES="60"

# Notifications
EXPO_PUBLIC_DEFAULT_ENABLE_APP_NOTIFICATIONS="true"
EXPO_PUBLIC_DEFAULT_ENABLE_WHATSAPP_NOTIFICATIONS="true"

# Testing
EXPO_PUBLIC_TEST_MODE="false"
EXPO_PUBLIC_MOCK_API_RESPONSES="false"
# EXPO_PUBLIC_TEST_USER_PHONE=""
# EXPO_PUBLIC_TEST_USER_OTP=""

# Logging
EXPO_PUBLIC_LOG_LEVEL="debug"
EXPO_PUBLIC_ENABLE_REMOTE_LOGGING="false"

# Performance
EXPO_PUBLIC_IMAGE_CACHE_SIZE_MB="50"
EXPO_PUBLIC_MAX_CONCURRENT_REQUESTS="6"
EXPO_PUBLIC_NETWORK_RETRY_ATTEMPTS="3"

# Accessibility
EXPO_PUBLIC_ENABLE_ACCESSIBILITY_FEATURES="true"
EXPO_PUBLIC_DEFAULT_FONT_SIZE="medium"
EXPO_PUBLIC_ENABLE_HIGH_CONTRAST="false"
EXPO_PUBLIC_ENABLE_REDUCED_MOTION="false"

# Third-party Services (Add actual values when available)
# EXPO_PUBLIC_SERVICE_GOOGLE_MAPS_API_KEY=""
# EXPO_PUBLIC_SERVICE_FIREBASE_PROJECT_ID=""
# EXPO_PUBLIC_SERVICE_FIREBASE_MESSAGING_SENDER_ID=""
# EXPO_PUBLIC_SERVICE_FIREBASE_APP_ID=""
# EXPO_PUBLIC_SERVICE_ANALYTICS_API_KEY=""
# EXPO_PUBLIC_SERVICE_ANALYTICS_ENDPOINT=""

# URLs
EXPO_PUBLIC_PRIVACY_POLICY_URL="https://example.com/privacy"
EXPO_PUBLIC_TERMS_OF_SERVICE_URL="https://example.com/terms"

# Expo specific
# EXPO_PROJECT_ID=""
EXPO_USE_UPDATES="true"
# EXPO_UPDATE_URL=""