/**
 * Configuration Module Index
 * 
 * This file exports all configuration-related utilities and constants
 * for easy importing throughout the application.
 */

// Export environment configuration
export { 
  env, 
  isDevelopment, 
  isStaging, 
  isProduction, 
  isDebugMode,
  type EnvironmentConfig 
} from './environment';

// Export validation utilities
export {
  validateEnvironment,
  validateEnvironmentOrThrow,
  logValidationResults,
  displayEnvironmentInfo,
  type ValidationResult
} from './validation';

// Export debug utilities
export * from './debug';

// Import env for default export
import { env } from './environment';

// Re-export for convenience
export default env;