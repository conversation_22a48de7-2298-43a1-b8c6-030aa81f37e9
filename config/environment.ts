import Constants from 'expo-constants';

/**
 * Environment Variable Configuration
 * 
 * This module provides type-safe access to environment variables with validation
 * and fallback values. It uses expo-constants to access environment variables
 * that are loaded via app.config.js.
 */

// Type definitions for our environment configuration
export interface EnvironmentConfig {
  // Application Environment
  app: {
    env: 'development' | 'staging' | 'production';
    debug: boolean;
    version: string;
  };

  // API Configuration
  api: {
    url: string;
    mediaUrl: string;
    timeout: number;
    refetchInterval: number;
    fileServerUrl: string;
  };

  // Authentication & Security
  auth: {
    clientId: string;
    redirectUri: string;
    refreshThresholdMinutes: number;
  };

  // Internationalization
  i18n: {
    defaultLanguage: string;
    fallbackLanguage: string;
    debug: boolean;
  };

  // Feature Flags
  features: {
    analytics: boolean;
    pushNotifications: boolean;
    biometricAuth: boolean;
    debugMenu: boolean;
    mockMode: boolean;
  };

  // UI & UX
  ui: {
    defaultThemeColor: string;
    animationSpeedMultiplier: number;
    enableHapticFeedback: boolean;
    fontScaleFactor: number;
  };

  // Development & Debugging
  development: {
    enableReactQueryDevtools: boolean;
    enableStateDevtools: boolean;
    enableNetworkLogging: boolean;
    enableFlipper: boolean;
    enablePerformanceMonitoring: boolean;
  };

  // Storage & Caching
  storage: {
    keyPrefix: string;
    cacheDurationMinutes: number;
    cacheMaxSizeMB: number;
  };

  // Security
  security: {
    enableCertificatePinning: boolean;
    enableSslVerification: boolean;
    requireBiometricAuth: boolean;
    sessionTimeoutMinutes: number;
  };

  // Notifications
  notifications: {
    defaultEnableApp: boolean;
    defaultEnableWhatsapp: boolean;
  };

  // Testing
  testing: {
    testMode: boolean;
    mockApiResponses: boolean;
    testUserPhone?: string;
    testUserOtp?: string;
  };

  // Logging
  logging: {
    level: 'debug' | 'info' | 'warn' | 'error';
    enableRemoteLogging: boolean;
  };

  // Performance
  performance: {
    imageCacheSizeMB: number;
    maxConcurrentRequests: number;
    networkRetryAttempts: number;
  };

  // Accessibility
  accessibility: {
    enableFeatures: boolean;
    defaultFontSize: string;
    enableHighContrast: boolean;
    enableReducedMotion: boolean;
  };

  // Third-party Services (optional)
  services: {
    googleMapsApiKey?: string;
    firebaseProjectId?: string;
    firebaseMessagingSenderId?: string;
    firebaseAppId?: string;
    analyticsApiKey?: string;
    analyticsEndpoint?: string;
  };

  // URLs
  urls: {
    privacyPolicy: string;
    termsOfService: string;
  };
}

/**
 * Utility function to parse boolean environment variables
 */
const parseBoolean = (value: string | undefined, defaultValue: boolean): boolean => {
  if (value === undefined) return defaultValue;
  return value.toLowerCase() === 'true';
};

/**
 * Utility function to parse number environment variables
 */
const parseNumber = (value: string | undefined, defaultValue: number): number => {
  if (value === undefined) return defaultValue;
  const parsed = parseInt(value, 10);
  return isNaN(parsed) ? defaultValue : parsed;
};

/**
 * Utility function to parse float environment variables
 */
const parseFloatEnv = (value: string | undefined, defaultValue: number): number => {
  if (value === undefined) return defaultValue;
  const parsed = parseFloat(value);
  return isNaN(parsed) ? defaultValue : parsed;
};

/**
 * Utility function to validate and parse log level
 */
const parseLogLevel = (value: string | undefined, defaultValue: 'debug' | 'info' | 'warn' | 'error'): 'debug' | 'info' | 'warn' | 'error' => {
  if (value === undefined) return defaultValue;
  const validLevels = ['debug', 'info', 'warn', 'error'] as const;
  return validLevels.includes(value as any) ? (value as 'debug' | 'info' | 'warn' | 'error') : defaultValue;
};

/**
 * Get environment variable with validation
 */
const getEnvVar = (key: string, required: boolean = false): string | undefined => {
  // In Expo, environment variables are available through Constants.expoConfig?.extra
  const value = Constants.expoConfig?.extra?.[key] || process.env[key];
  
  if (required && !value) {
    throw new Error(`Required environment variable ${key} is not set`);
  }
  
  return value;
};

/**
 * Validate required environment variables
 */
const validateRequiredVars = (): void => {
  const requiredVars = ['API_URL'];
  
  const missingVars = requiredVars.filter(varName => {
    const value = getEnvVar(varName);
    return !value || value.trim() === '';
  });

  if (missingVars.length > 0) {
    throw new Error(
      `Missing required environment variables: ${missingVars.join(', ')}\n` +
      'Please check your .env file and ensure all required variables are set.'
    );
  }
};

/**
 * Create and validate environment configuration
 */
const createEnvironmentConfig = (): EnvironmentConfig => {
  // Validate required variables first
  validateRequiredVars();

  const appEnv = getEnvVar('APP_ENV', false) || 'development';
  
  // Ensure app environment is valid
  if (!['development', 'staging', 'production'].includes(appEnv)) {
    throw new Error(`Invalid APP_ENV value: ${appEnv}. Must be one of: development, staging, production`);
  }

  return {
    app: {
      env: appEnv as 'development' | 'staging' | 'production',
      debug: parseBoolean(getEnvVar('APP_DEBUG'), appEnv === 'development'),
      version: getEnvVar('APP_VERSION') || '1.0.0',
    },

    api: {
      url: getEnvVar('API_URL', true)!,
      mediaUrl: getEnvVar('MEDIA_BASE_URL') || getEnvVar('API_URL', true)!.replace('/api/v1', ''),
      timeout: parseNumber(getEnvVar('API_TIMEOUT'), 30000),
      refetchInterval: parseNumber(getEnvVar('API_REFETCH_INTERVAL'), 60000),
      fileServerUrl: getEnvVar('FILE_SERVER_URL') || getEnvVar('API_URL', true)!.replace('/api/v1', ''),
    },

    auth: {
      clientId: getEnvVar('AUTH_CLIENT_ID') || 'mobile-app-client',
      redirectUri: getEnvVar('AUTH_REDIRECT_URI') || 'themoment://auth',
      refreshThresholdMinutes: parseNumber(getEnvVar('AUTH_REFRESH_THRESHOLD_MINUTES'), 5),
    },

    i18n: {
      defaultLanguage: getEnvVar('DEFAULT_LANGUAGE') || 'zh-HK',
      fallbackLanguage: getEnvVar('FALLBACK_LANGUAGE') || 'en',
      debug: parseBoolean(getEnvVar('I18N_DEBUG'), appEnv === 'development'),
    },

    features: {
      analytics: parseBoolean(getEnvVar('FEATURE_ENABLE_ANALYTICS'), false),
      pushNotifications: parseBoolean(getEnvVar('FEATURE_ENABLE_PUSH_NOTIFICATIONS'), true),
      biometricAuth: parseBoolean(getEnvVar('FEATURE_ENABLE_BIOMETRIC_AUTH'), false),
      debugMenu: parseBoolean(getEnvVar('FEATURE_ENABLE_DEBUG_MENU'), appEnv === 'development'),
      mockMode: parseBoolean(getEnvVar('FEATURE_MOCK_MODE'), false),
    },

    ui: {
      defaultThemeColor: getEnvVar('DEFAULT_THEME_COLOR') || 'red',
      animationSpeedMultiplier: parseFloatEnv(getEnvVar('ANIMATION_SPEED_MULTIPLIER'), 1.0),
      enableHapticFeedback: parseBoolean(getEnvVar('ENABLE_HAPTIC_FEEDBACK'), true),
      fontScaleFactor: parseFloatEnv(getEnvVar('FONT_SCALE_FACTOR'), 1.0),
    },

    development: {
      enableReactQueryDevtools: parseBoolean(getEnvVar('ENABLE_REACT_QUERY_DEVTOOLS'), appEnv === 'development'),
      enableStateDevtools: parseBoolean(getEnvVar('ENABLE_STATE_DEVTOOLS'), appEnv === 'development'),
      enableNetworkLogging: parseBoolean(getEnvVar('ENABLE_NETWORK_LOGGING'), appEnv === 'development'),
      enableFlipper: parseBoolean(getEnvVar('ENABLE_FLIPPER'), appEnv === 'development'),
      enablePerformanceMonitoring: parseBoolean(getEnvVar('ENABLE_PERFORMANCE_MONITORING'), false),
    },

    storage: {
      keyPrefix: getEnvVar('STORAGE_KEY_PREFIX') || 'themoment_',
      cacheDurationMinutes: parseNumber(getEnvVar('CACHE_DURATION_MINUTES'), 60),
      cacheMaxSizeMB: parseNumber(getEnvVar('CACHE_MAX_SIZE_MB'), 100),
    },

    security: {
      enableCertificatePinning: parseBoolean(getEnvVar('ENABLE_CERTIFICATE_PINNING'), appEnv === 'production'),
      enableSslVerification: parseBoolean(getEnvVar('ENABLE_SSL_VERIFICATION'), true),
      requireBiometricAuth: parseBoolean(getEnvVar('REQUIRE_BIOMETRIC_AUTH'), false),
      sessionTimeoutMinutes: parseNumber(getEnvVar('SESSION_TIMEOUT_MINUTES'), 60),
    },

    notifications: {
      defaultEnableApp: parseBoolean(getEnvVar('DEFAULT_ENABLE_APP_NOTIFICATIONS'), true),
      defaultEnableWhatsapp: parseBoolean(getEnvVar('DEFAULT_ENABLE_WHATSAPP_NOTIFICATIONS'), true),
    },

    testing: {
      testMode: parseBoolean(getEnvVar('TEST_MODE'), false),
      mockApiResponses: parseBoolean(getEnvVar('MOCK_API_RESPONSES'), false),
      testUserPhone: getEnvVar('TEST_USER_PHONE'),
      testUserOtp: getEnvVar('TEST_USER_OTP'),
    },

    logging: {
      level: parseLogLevel(getEnvVar('LOG_LEVEL'), 'debug'),
      enableRemoteLogging: parseBoolean(getEnvVar('ENABLE_REMOTE_LOGGING'), false),
    },

    performance: {
      imageCacheSizeMB: parseNumber(getEnvVar('IMAGE_CACHE_SIZE_MB'), 50),
      maxConcurrentRequests: parseNumber(getEnvVar('MAX_CONCURRENT_REQUESTS'), 6),
      networkRetryAttempts: parseNumber(getEnvVar('NETWORK_RETRY_ATTEMPTS'), 3),
    },

    accessibility: {
      enableFeatures: parseBoolean(getEnvVar('ENABLE_ACCESSIBILITY_FEATURES'), true),
      defaultFontSize: getEnvVar('DEFAULT_FONT_SIZE') || 'medium',
      enableHighContrast: parseBoolean(getEnvVar('ENABLE_HIGH_CONTRAST'), false),
      enableReducedMotion: parseBoolean(getEnvVar('ENABLE_REDUCED_MOTION'), false),
    },

    services: {
      googleMapsApiKey: getEnvVar('SERVICE_GOOGLE_MAPS_API_KEY'),
      firebaseProjectId: getEnvVar('SERVICE_FIREBASE_PROJECT_ID'),
      firebaseMessagingSenderId: getEnvVar('SERVICE_FIREBASE_MESSAGING_SENDER_ID'),
      firebaseAppId: getEnvVar('SERVICE_FIREBASE_APP_ID'),
      analyticsApiKey: getEnvVar('SERVICE_ANALYTICS_API_KEY'),
      analyticsEndpoint: getEnvVar('SERVICE_ANALYTICS_ENDPOINT'),
    },

    urls: {
      privacyPolicy: getEnvVar('PRIVACY_POLICY_URL') || 'https://example.com/privacy',
      termsOfService: getEnvVar('TERMS_OF_SERVICE_URL') || 'https://example.com/terms',
    },
  };
};

/**
 * Environment configuration instance
 * This will be created once when the module is imported
 */
let environmentConfig: EnvironmentConfig;

try {
  environmentConfig = createEnvironmentConfig();
  
  // Log configuration in development
  if (environmentConfig.app.debug) {
    console.log('[Environment] Configuration loaded successfully:', {
      env: environmentConfig.app.env,
      apiUrl: environmentConfig.api.url,
      debug: environmentConfig.app.debug,
      features: environmentConfig.features,
    });
  }
} catch (error) {
  console.error('[Environment] Failed to load configuration:', error);
  throw error;
}

/**
 * Export the environment configuration
 */
export const env = environmentConfig;

/**
 * Utility functions for common environment checks
 */
export const isDevelopment = env.app.env === 'development';
export const isStaging = env.app.env === 'staging';
export const isProduction = env.app.env === 'production';
export const isDebugMode = env.app.debug;

/**
 * Export default configuration
 */
export default env;