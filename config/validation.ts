import { env } from './environment';

/**
 * Environment Configuration Validation Utilities
 * 
 * This module provides utilities to validate environment configuration
 * and provide helpful error messages for common configuration issues.
 */

export interface ValidationResult {
  isValid: boolean;
  errors: string[];
  warnings: string[];
}

/**
 * Validate URL format
 */
const isValidUrl = (url: string): boolean => {
  try {
    new URL(url);
    return true;
  } catch {
    return false;
  }
};

/**
 * Validate required API configuration
 */
const validateApiConfig = (): { errors: string[]; warnings: string[] } => {
  const errors: string[] = [];
  const warnings: string[] = [];

  // Validate API URL
  if (!env.api.url) {
    errors.push('API_URL is required but not set');
  } else if (!isValidUrl(env.api.url)) {
    errors.push(`API_URL is not a valid URL: ${env.api.url}`);
  } else {
    // Check for common URL issues
    if (env.api.url.endsWith('/')) {
      warnings.push('API_URL should not end with a trailing slash');
    }
    if (!env.api.url.includes('/api/v1')) {
      warnings.push('API_URL should typically include /api/v1 path');
    }
  }

  // Validate timeout values
  if (env.api.timeout < 5000) {
    warnings.push('API_TIMEOUT is very low (< 5 seconds), this may cause request failures');
  }
  if (env.api.timeout > 60000) {
    warnings.push('API_TIMEOUT is very high (> 60 seconds), this may impact user experience');
  }

  return { errors, warnings };
};

/**
 * Validate authentication configuration
 */
const validateAuthConfig = (): { errors: string[]; warnings: string[] } => {
  const errors: string[] = [];
  const warnings: string[] = [];

  // Validate client ID
  if (!env.auth.clientId) {
    warnings.push('AUTH_CLIENT_ID is not set, using default value');
  }

  // Validate redirect URI format
  if (!env.auth.redirectUri.includes('://')) {
    errors.push('AUTH_REDIRECT_URI must be a valid URI scheme (e.g., themoment://auth)');
  }

  return { errors, warnings };
};

/**
 * Validate development configuration
 */
const validateDevelopmentConfig = (): { errors: string[]; warnings: string[] } => {
  const errors: string[] = [];
  const warnings: string[] = [];

  // Check for development-specific issues
  if (env.app.env === 'production') {
    if (env.app.debug) {
      warnings.push('APP_DEBUG is enabled in production environment');
    }
    if (env.development.enableNetworkLogging) {
      warnings.push('Network logging is enabled in production environment');
    }
    if (env.i18n.debug) {
      warnings.push('I18N_DEBUG is enabled in production environment');
    }
  }

  return { errors, warnings };
};

/**
 * Validate storage configuration
 */
const validateStorageConfig = (): { errors: string[]; warnings: string[] } => {
  const errors: string[] = [];
  const warnings: string[] = [];

  // Validate storage key prefix
  if (!env.storage.keyPrefix) {
    warnings.push('STORAGE_KEY_PREFIX is not set, this may cause storage conflicts');
  } else if (!env.storage.keyPrefix.endsWith('_')) {
    warnings.push('STORAGE_KEY_PREFIX should end with underscore for clarity');
  }

  // Validate cache settings
  if (env.storage.cacheMaxSizeMB > 500) {
    warnings.push('Cache size is very large (> 500MB), this may impact device storage');
  }

  return { errors, warnings };
};

/**
 * Validate performance configuration
 */
const validatePerformanceConfig = (): { errors: string[]; warnings: string[] } => {
  const errors: string[] = [];
  const warnings: string[] = [];

  // Validate performance settings
  if (env.performance.maxConcurrentRequests > 10) {
    warnings.push('High number of concurrent requests may impact performance');
  }

  if (env.performance.networkRetryAttempts > 5) {
    warnings.push('High retry attempts may cause poor user experience on network failures');
  }

  return { errors, warnings };
};

/**
 * Validate UI configuration
 */
const validateUIConfig = (): { errors: string[]; warnings: string[] } => {
  const errors: string[] = [];
  const warnings: string[] = [];

  // Validate theme color
  const validThemes = ['red', 'blue', 'green', 'orange', 'pink', 'whatsapp'];
  if (!validThemes.includes(env.ui.defaultThemeColor)) {
    errors.push(`Invalid DEFAULT_THEME_COLOR: ${env.ui.defaultThemeColor}. Must be one of: ${validThemes.join(', ')}`);
  }

  // Validate animation speed
  if (env.ui.animationSpeedMultiplier < 0.1 || env.ui.animationSpeedMultiplier > 3.0) {
    warnings.push('Animation speed multiplier is outside recommended range (0.1 - 3.0)');
  }

  return { errors, warnings };
};

/**
 * Validate language configuration
 */
const validateLanguageConfig = (): { errors: string[]; warnings: string[] } => {
  const errors: string[] = [];
  const warnings: string[] = [];

  const supportedLanguages = ['en', 'zh-HK'];
  
  if (!supportedLanguages.includes(env.i18n.defaultLanguage)) {
    errors.push(`Unsupported DEFAULT_LANGUAGE: ${env.i18n.defaultLanguage}. Supported: ${supportedLanguages.join(', ')}`);
  }

  if (!supportedLanguages.includes(env.i18n.fallbackLanguage)) {
    errors.push(`Unsupported FALLBACK_LANGUAGE: ${env.i18n.fallbackLanguage}. Supported: ${supportedLanguages.join(', ')}`);
  }

  return { errors, warnings };
};

/**
 * Comprehensive environment validation
 */
export const validateEnvironment = (): ValidationResult => {
  const allErrors: string[] = [];
  const allWarnings: string[] = [];

  // Run all validation checks
  const validations = [
    validateApiConfig,
    validateAuthConfig,
    validateDevelopmentConfig,
    validateStorageConfig,
    validatePerformanceConfig,
    validateUIConfig,
    validateLanguageConfig,
  ];

  validations.forEach((validate) => {
    const { errors, warnings } = validate();
    allErrors.push(...errors);
    allWarnings.push(...warnings);
  });

  return {
    isValid: allErrors.length === 0,
    errors: allErrors,
    warnings: allWarnings,
  };
};

/**
 * Log validation results
 */
export const logValidationResults = (result: ValidationResult): void => {
  if (result.isValid) {
    console.log('✅ [Environment] Configuration validation passed');
  } else {
    console.error('❌ [Environment] Configuration validation failed');
    result.errors.forEach((error) => {
      console.error(`   • ${error}`);
    });
  }

  if (result.warnings.length > 0) {
    console.warn('⚠️  [Environment] Configuration warnings:');
    result.warnings.forEach((warning) => {
      console.warn(`   • ${warning}`);
    });
  }
};

/**
 * Validate environment and throw error if invalid
 */
export const validateEnvironmentOrThrow = (): void => {
  const result = validateEnvironment();
  logValidationResults(result);

  if (!result.isValid) {
    throw new Error(
      'Environment configuration validation failed. Please check your .env file and fix the errors listed above.'
    );
  }
};

/**
 * Display environment information for debugging
 */
export const displayEnvironmentInfo = (): void => {
  if (!env.app.debug) return;

  console.log('🔧 [Environment] Current configuration:');
  console.log(`   Environment: ${env.app.env}`);
  console.log(`   Debug Mode: ${env.app.debug}`);
  console.log(`   API URL: ${env.api.url}`);
  console.log(`   Default Language: ${env.i18n.defaultLanguage}`);
  console.log(`   Theme Color: ${env.ui.defaultThemeColor}`);
  console.log(`   Network Logging: ${env.development.enableNetworkLogging}`);
};