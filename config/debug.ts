import { env } from './environment';

/**
 * Debug and Development Utilities
 * 
 * This module provides utilities for debugging and development
 * that are controlled by environment variables.
 */

/**
 * Enhanced console logging that respects environment settings
 */
export class Logger {
  private static shouldLog(level: 'debug' | 'info' | 'warn' | 'error'): boolean {
    if (!env.app.debug && level === 'debug') return false;
    
    const levels = ['debug', 'info', 'warn', 'error'];
    const currentLevelIndex = levels.indexOf(env.logging.level);
    const messageLevelIndex = levels.indexOf(level);
    
    return messageLevelIndex >= currentLevelIndex;
  }

  static debug(message: string, ...args: any[]): void {
    if (this.shouldLog('debug')) {
      console.log(`🔍 [DEBUG] ${message}`, ...args);
    }
  }

  static info(message: string, ...args: any[]): void {
    if (this.shouldLog('info')) {
      console.log(`ℹ️  [INFO] ${message}`, ...args);
    }
  }

  static warn(message: string, ...args: any[]): void {
    if (this.shouldLog('warn')) {
      console.warn(`⚠️  [WARN] ${message}`, ...args);
    }
  }

  static error(message: string, ...args: any[]): void {
    if (this.shouldLog('error')) {
      console.error(`❌ [ERROR] ${message}`, ...args);
    }
  }

  static network(message: string, ...args: any[]): void {
    if (env.development.enableNetworkLogging) {
      console.log(`🌐 [NETWORK] ${message}`, ...args);
    }
  }

  static performance(message: string, ...args: any[]): void {
    if (env.development.enablePerformanceMonitoring) {
      console.log(`⚡ [PERF] ${message}`, ...args);
    }
  }
}

/**
 * Performance monitoring utilities
 */
export class PerformanceMonitor {
  private static timers: Map<string, number> = new Map();

  static start(label: string): void {
    if (!env.development.enablePerformanceMonitoring) return;
    this.timers.set(label, Date.now());
  }

  static end(label: string): number | null {
    if (!env.development.enablePerformanceMonitoring) return null;
    
    const startTime = this.timers.get(label);
    if (!startTime) {
      Logger.warn(`Performance timer '${label}' was not started`);
      return null;
    }

    const duration = Date.now() - startTime;
    this.timers.delete(label);
    Logger.performance(`${label}: ${duration}ms`);
    return duration;
  }

  static measure<T>(label: string, fn: () => T): T {
    if (!env.development.enablePerformanceMonitoring) {
      return fn();
    }

    this.start(label);
    try {
      const result = fn();
      this.end(label);
      return result;
    } catch (error) {
      this.end(label);
      throw error;
    }
  }

  static async measureAsync<T>(label: string, fn: () => Promise<T>): Promise<T> {
    if (!env.development.enablePerformanceMonitoring) {
      return fn();
    }

    this.start(label);
    try {
      const result = await fn();
      this.end(label);
      return result;
    } catch (error) {
      this.end(label);
      throw error;
    }
  }
}

/**
 * Feature flag utilities
 */
export class FeatureFlags {
  static isEnabled(flag: keyof typeof env.features): boolean {
    return env.features[flag];
  }

  static getEnabledFeatures(): string[] {
    return Object.entries(env.features)
      .filter(([, enabled]) => enabled)
      .map(([feature]) => feature);
  }

  static logEnabledFeatures(): void {
    if (env.app.debug) {
      const enabled = this.getEnabledFeatures();
      Logger.debug('Enabled features:', enabled);
    }
  }
}

/**
 * Development menu utilities (for debug builds)
 */
export class DevelopmentMenu {
  static shouldShow(): boolean {
    return env.features.debugMenu && env.app.env === 'development';
  }

  static getMenuItems(): Array<{ label: string; action: () => void }> {
    if (!this.shouldShow()) return [];

    return [
      {
        label: 'Clear Storage',
        action: () => {
          // This would clear AsyncStorage in a real implementation
          Logger.info('Storage cleared (mock)');
        },
      },
      {
        label: 'Force Logout',
        action: () => {
          // This would force logout in a real implementation
          Logger.info('Force logout (mock)');
        },
      },
      {
        label: 'Toggle Network Logging',
        action: () => {
          // This would toggle network logging in a real implementation
          Logger.info('Network logging toggled (mock)');
        },
      },
      {
        label: 'Show Environment Info',
        action: () => {
          Logger.info('Environment Info:', {
            env: env.app.env,
            debug: env.app.debug,
            apiUrl: env.api.url,
            features: env.features,
          });
        },
      },
    ];
  }
}

/**
 * API mocking utilities for testing
 */
export class MockUtils {
  static shouldUseMocks(): boolean {
    return env.features.mockMode || env.testing.mockApiResponses;
  }

  static createMockDelay(): Promise<void> {
    if (!this.shouldUseMocks()) {
      return Promise.resolve();
    }

    // Simulate network delay in mock mode
    const delay = Math.random() * 1000 + 500; // 500-1500ms
    return new Promise(resolve => setTimeout(resolve, delay));
  }

  static createMockResponse<T>(data: T, shouldFail: boolean = false): Promise<T> {
    return this.createMockDelay().then(() => {
      if (shouldFail) {
        throw new Error('Mock API error');
      }
      return data;
    });
  }
}

/**
 * Export all debug utilities
 */
export {
  Logger as log,
  PerformanceMonitor as perf,
  FeatureFlags as features,
  DevelopmentMenu as devMenu,
  MockUtils as mock,
};