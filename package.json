{"name": "the-moment", "version": "1.0.0", "main": "expo-router/entry", "scripts": {"start": "expo start", "android": "expo run:android", "ios": "expo run:ios", "web": "expo start --web", "build:local": "eas build --local", "build:local:android": "eas build --local --platform android", "build:local:ios": "eas build --local --platform ios", "build:local:dev": "eas build --local --profile development", "build:local:preview": "eas build --local --profile preview", "test": "jest --config jest.config.new.js", "test:watch": "jest --config jest.config.new.js --watch", "test:coverage": "jest --config jest.config.new.js --coverage", "test:ci": "jest --config jest.config.new.js --ci --coverage --maxWorkers=2"}, "dependencies": {"@react-native-async-storage/async-storage": "^2.1.2", "@react-native-picker/picker": "2.11.1", "@react-navigation/material-top-tabs": "^7.2.13", "@react-navigation/native": "^7.1.6", "@react-navigation/native-stack": "^7.3.10", "@tanstack/react-query": "^5.76.2", "axios": "^1.9.0", "base-64": "^1.0.0", "country-list-with-dial-code-and-flag": "^5.1.0", "date-fns": "^4.1.0", "date-fns-tz": "^3.2.0", "expo": "53.0.19", "expo-blur": "~14.1.5", "expo-camera": "~16.1.10", "expo-constants": "^17.1.6", "expo-crypto": "~14.1.5", "expo-dev-client": "~5.2.4", "expo-document-picker": "~13.1.6", "expo-file-system": "~18.1.11", "expo-image": "~2.3.2", "expo-image-picker": "^16.1.4", "expo-linear-gradient": "~14.1.5", "expo-media-library": "~17.1.7", "expo-router": "~5.1.3", "expo-sharing": "^13.1.5", "expo-splash-screen": "~0.30.10", "expo-status-bar": "~2.2.3", "expo-updates": "~0.28.17", "expo-video": "~2.2.2", "ics": "^3.8.1", "pretty-format": "^30.0.2", "react": "19.0.0", "react-i18next": "^15.5.2", "react-native": "0.79.5", "react-native-chart-kit": "^6.12.0", "react-native-dotenv": "^3.4.11", "react-native-dropdown-picker": "^5.4.6", "react-native-image-viewing": "^0.2.2", "react-native-pager-view": "6.7.1", "react-native-paper": "^5.14.5", "react-native-qrcode-svg": "^6.3.15", "react-native-reanimated": "^3.17.5", "react-native-safe-area-context": "5.4.0", "react-native-screens": "~4.11.1", "react-native-svg": "15.11.2", "zustand": "^5.0.5"}, "devDependencies": {"@babel/core": "^7.25.2", "@babel/preset-typescript": "^7.27.1", "@react-native-community/cli": "^18.0.0", "@testing-library/jest-native": "^5.4.3", "@testing-library/react-native": "^13.2.0", "@types/base-64": "^1.0.2", "@types/jest": "^30.0.0", "@types/node": "^22.15.21", "@types/react": "~19.0.10", "ajv": "^8.17.1", "ajv-formats": "^3.0.1", "babel-plugin-module-resolver": "^5.0.2", "eas-cli": "^16.12.0", "jest": "~29.7.0", "jest-expo": "^53.0.9", "msw": "^2.10.3", "ts-jest": "^29.4.0", "typescript": "~5.8.3"}, "private": true}