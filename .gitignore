# Learn more https://docs.github.com/en/get-started/getting-started-with-git/ignoring-files

# Dependencies
node_modules/
.pnp
.pnp.js

# Testing
coverage/
.nyc_output
__tests__/coverage/
jest/
test-results/
*.lcov

# Expo
.expo/
.expo-shared/
dist/
web-build/
expo-env.d.ts
*.jks
*.p8
*.p12
*.key
*.mobileprovision
*.orig.*

# React Native
.bundle/

# Android/IntelliJ
android/
build/
.idea/
.gradle/
local.properties
*.iml
*.hprof

# iOS
ios/
.xcode.env
Pods/
*.pbxuser
!default.pbxuser
*.mode1v3
!default.mode1v3
*.mode2v3
!default.mode2v3
*.perspectivev3
!default.perspectivev3
xcuserdata
*.xccheckout
*.moved-aside
DerivedData
*.hmap
*.ipa
*.xcuserstate
*.dSYM.zip
*.dSYM

# Metro
.metro-health-check*
.metro-cache/

# Debug
npm-debug.*
yarn-debug.*
yarn-error.*
lerna-debug.*
.npm

# IDE - VSCode
.vscode/*
!.vscode/settings.json
!.vscode/tasks.json
!.vscode/launch.json
!.vscode/extensions.json
*.code-workspace

# IDE - JetBrains
.idea/
*.iml
*.iws
*.ipr

# OS files
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db
*~

# Environment files
.env
.env*.local
.env.development.local
.env.test.local
.env.production.local

# TypeScript
*.tsbuildinfo
tsconfig.tsbuildinfo

# Temporary files
*.log
*.tmp
*.temp
.cache/
tmp/
temp/

# Security
*.pem
*.p12
*.key
*.mobileprovision
*.keystore

# Misc
.eslintcache
.stylelintcache
*.swp
*.swo
*.swn
*.bak

# CI/CD
.github/workflows/artifacts/

# Documentation build
docs/_build/
docs/.docusaurus/

# Yarn
.yarn/*
!.yarn/releases
!.yarn/plugins
!.yarn/sdks
!.yarn/versions
.pnp.*

# npm
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# Optional npm cache directory
.npm

# Optional eslint cache
.eslintcache

# Optional REPL history
.node_repl_history

# Output of 'npm pack'
*.tgz

# Yarn Integrity file
.yarn-integrity

# dotenv environment variables file
.env
.env.test

# parcel-bundler cache (https://parceljs.org/)
.cache
.parcel-cache

# Next.js build output
.next
out

# Nuxt.js build / generate output
.nuxt
dist

# Gatsby files
.cache/
# Comment in the public line in if your project uses Gatsby and not Next.js
# https://nextjs.org/blog/next-9-1#public-directory-support
# public

# Serverless directories
.serverless/

# FuseBox cache
.fusebox/

# DynamoDB Local files
.dynamodb/

# TernJS port file
.tern-port

# Stores VSCode versions used for testing VSCode extensions
.vscode-test

# Temporary folders
tmp/
temp/

.tar.gz